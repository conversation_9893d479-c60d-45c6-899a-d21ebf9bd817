# Voice AI Backend

<div align="center">
  <h3>🎤 Modern Voice AI Backend with Real-time Speech Processing</h3>
  <p>A production-ready voice AI system supporting both Google Cloud and Azure services</p>

  ![Node.js](https://img.shields.io/badge/Node.js-18+-green)
  ![TypeScript](https://img.shields.io/badge/Architecture-Clean-blue)
  ![Socket.IO](https://img.shields.io/badge/Socket.IO-Real--time-orange)
  ![License](https://img.shields.io/badge/License-MIT-yellow)
</div>

## 🚀 Overview

This Voice AI Backend provides a complete solution for real-time voice interaction applications. It supports multiple AI providers (Google Cloud, Azure) with automatic fallback, real-time speech recognition, AI-powered responses, and text-to-speech generation.

### ✨ Key Features

- **🎯 Multi-Provider Support**: Google Cloud (Vertex AI, Speech-to-Text) and Azure (OpenAI, Cognitive Services)
- **⚡ Real-time Processing**: WebSocket-based audio streaming with sub-second latency
- **🔄 Automatic Fallback**: Seamless provider switching for high availability
- **🛡️ Enterprise Security**: API key authentication, rate limiting, CORS protection
- **📊 Monitoring**: Built-in performance metrics, health checks, and logging
- **🏗️ Clean Architecture**: Modular design with interfaces and dependency injection
- **🐳 Container Ready**: Docker support for easy deployment
- **☁️ Cloud Native**: Deploy to Google Cloud Run, Azure Container Apps, or any Kubernetes cluster

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Voice AI      │    │   AI Providers  │
│   (React/JS)    │◄──►│   Backend       │◄──►│  (Google/Azure) │
│                 │    │                 │    │                 │
│ • Microphone    │    │ • WebSockets    │    │ • Speech-to-Text│
│ • Audio Player  │    │ • REST APIs     │    │ • AI Generation │
│ • UI Controls   │    │ • Real-time     │    │ • Text-to-Speech│
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 📁 Project Structure

```
voice-ai-backend/
├── 📁 api/                    # REST API Layer
│   ├── controllers/           # Route controllers
│   ├── middleware/           # Request validation & logging
│   └── routes/               # API route definitions
├── 📁 core/                  # Core Framework
│   ├── config/               # Environment & security config
│   ├── utils/                # Utilities (logging, validation)
│   └── types/                # TypeScript-style definitions
├── 📁 services/              # Business Logic Layer
│   ├── interfaces/           # Service contracts
│   ├── providers/            # AI provider implementations
│   │   ├── google/           # Google Cloud services
│   │   └── azure/            # Azure services
│   └── factory/              # Service factory & config
├── 📁 socket/                # WebSocket Layer
│   ├── handlers/             # Socket event handlers
│   └── middleware/           # Socket authentication & validation
├── 📁 docs/                  # Documentation
└── 📄 index.js               # Application entry point
```

## 🛠️ Installation & Setup

### Prerequisites

- **Node.js** 18+
- **npm** or **yarn**
- **Google Cloud** or **Azure** account with AI services enabled
- **API Keys** for your chosen provider(s)

### 1. Clone & Install

```bash
git clone <your-repo-url>
cd voice-ai-backend
npm install
```

### 2. Environment Configuration

Create a `.env` file in the root directory:

```env
# Server Configuration
NODE_ENV=development
PORT=3001
API_KEY=your_secure_api_key_here

# Google Cloud Configuration (Optional)
GOOGLE_CLOUD_PROJECT=your-project-id
GOOGLE_CLOUD_LOCATION=europe-southwest1
AUDIO_BACKEND_BASE_URL=https://your-tts-service.com/api/v1
AUDIO_BACKEND_API_KEY=Bearer your_tts_api_key

# Azure Configuration (Optional)
AZURE_SPEECH_KEY=your_azure_speech_key
AZURE_SPEECH_REGION=westeurope
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com
AZURE_OPENAI_KEY=your_azure_openai_key
AZURE_OPENAI_DEPLOYMENT_NAME=gpt-4

# AI Configuration
AI_PROMPT="You are a helpful AI assistant..."
DEFAULT_PROVIDER=google

# Frontend URLs
FRONTEND_DEV_URL=http://localhost:3000
FRONTEND_PRO_URL=https://your-production-domain.com
```

### 3. Start Development Server

```bash
npm run dev
```

The server will start on `http://localhost:3001` with hot reload enabled.

## 🔧 Configuration

### Provider Setup

#### Google Cloud Setup

1. **Enable APIs** in Google Cloud Console:
   - Speech-to-Text API
   - Vertex AI API
   - Text-to-Speech API (if using custom TTS)

2. **Authentication**:
   ```bash
   # Option 1: Service Account Key
   export GOOGLE_APPLICATION_CREDENTIALS="path/to/service-account.json"

   # Option 2: Application Default Credentials
   gcloud auth application-default login
   ```

3. **Required Environment Variables**:
   ```env
   GOOGLE_CLOUD_PROJECT=your-project-id
   GOOGLE_CLOUD_LOCATION=europe-southwest1
   AUDIO_BACKEND_BASE_URL=https://your-tts-service.com
   AUDIO_BACKEND_API_KEY=Bearer your_api_key
   ```

#### Azure Setup

1. **Create Resources** in Azure Portal:
   - Speech Services
   - OpenAI Service
   - Deploy GPT-4 model

2. **Required Environment Variables**:
   ```env
   AZURE_SPEECH_KEY=your_speech_service_key
   AZURE_SPEECH_REGION=westeurope
   AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com
   AZURE_OPENAI_KEY=your_openai_key
   AZURE_OPENAI_DEPLOYMENT_NAME=gpt-4
   ```

### Advanced Configuration

```javascript
// core/config/environment.js - Key configuration options

{
  server: {
    port: 3001,
    logLevel: 'debug'
  },
  security: {
    enableRateLimit: true,
    corsOrigins: ['http://localhost:3000'],
    sessionTimeout: 1800000  // 30 minutes
  },
  providers: {
    default: 'google',
    enabled: ['google', 'azure']
  }
}
```

## 📡 API Documentation

### Health Endpoints

#### `GET /health`
Basic health check (no authentication required)

```bash
curl http://localhost:3001/health
```

**Response:**
```json
{
  "status": "ok",
  "timestamp": "2025-01-21T10:30:00.000Z",
  "environment": "development",
  "uptime": 3600,
  "memory": {
    "rss": 85,
    "heapTotal": 45,
    "heapUsed": 32,
    "unit": "MB"
  }
}
```

#### `GET /api/health/detailed`
Comprehensive health check with service status

```bash
curl -H "X-API-Key: your_api_key" http://localhost:3001/api/health/detailed
```

**Response:**
```json
{
  "status": "ok",
  "provider": "google",
  "services": {
    "speech": {
      "healthy": true,
      "provider": "google"
    },
    "ai": {
      "healthy": true,
      "provider": "google"
    },
    "tts": {
      "healthy": true,
      "provider": "google"
    }
  },
  "performance": {
    "totalRequests": 145,
    "averageResponseTime": 250
  }
}
```

### Configuration Endpoints

#### `GET /api/config/summary/:provider`
Get configuration summary for a provider

```bash
curl -H "X-API-Key: your_api_key" \
     http://localhost:3001/api/config/summary/google
```

#### `GET /api/config/voices/:provider`
Get available TTS voices

```bash
curl -H "X-API-Key: your_api_key" \
     http://localhost:3001/api/config/voices/azure
```

**Response:**
```json
{
  "provider": "azure",
  "voices": [
    {
      "name": "es-ES-ElviraNeural",
      "gender": "female",
      "language": "Spanish (Spain)"
    }
  ]
}
```

## 🔌 WebSocket API

### Connection

Connect to the WebSocket server with authentication:

```javascript
const socket = io('http://localhost:3001', {
  auth: {
    apiKey: 'your_api_key'
  },
  query: {
    provider: 'google'  // or 'azure'
  }
});
```

### Events

#### Client → Server

| Event | Description | Payload |
|-------|-------------|---------|
| `audio` | Send audio data | `ArrayBuffer` (audio bytes) |
| `mute-sound` | Mute TTS audio | - |
| `unmute-sound` | Unmute TTS audio | - |
| `end-session` | End voice session | - |
| `ping` | Keep connection alive | - |

#### Server → Client

| Event | Description | Payload |
|-------|-------------|---------|
| `transcription` | Speech recognition result | `string` (transcribed text) |
| `reply` | AI response with optional audio | `{text: string, audioUrl?: string}` |
| `loading` | Processing indicator | - |
| `error` | Error notification | `{code: string, message: string}` |
| `pong` | Ping response | - |

### Example Frontend Integration

```javascript
// Initialize WebSocket connection
const socket = io('http://localhost:3001', {
  auth: { apiKey: 'your_api_key' },
  query: { provider: 'google' }
});

// Handle audio recording
let mediaRecorder;
let audioStream;

async function startRecording() {
  audioStream = await navigator.mediaDevices.getUserMedia({
    audio: {
      sampleRate: 16000,
      channelCount: 1,
      echoCancellation: true,
      noiseSuppression: true
    }
  });

  mediaRecorder = new MediaRecorder(audioStream, {
    mimeType: 'audio/webm;codecs=opus'
  });

  mediaRecorder.ondataavailable = (event) => {
    if (event.data.size > 0) {
      // Convert to ArrayBuffer and send
      event.data.arrayBuffer().then(buffer => {
        socket.emit('audio', buffer);
      });
    }
  };

  mediaRecorder.start(100); // Send data every 100ms
}

// Handle responses
socket.on('transcription', (text) => {
  console.log('User said:', text);
  document.getElementById('transcription').textContent = text;
});

socket.on('reply', (response) => {
  console.log('AI replied:', response.text);
  document.getElementById('ai-response').textContent = response.text;

  // Play audio if available
  if (response.audioUrl && !isMuted) {
    const audio = new Audio(response.audioUrl);
    audio.play();
  }
});

socket.on('error', (error) => {
  console.error('Voice AI Error:', error);
  alert(`Error: ${error.message}`);
});
```

## 🔧 Development

### Available Scripts

```bash
# Development with hot reload
npm run dev

# Production start
npm start

# Run tests
npm test

# Health check
npm run test:health

# Test individual services
npm run test:services

# Linting
npm run lint
npm run lint:fix

# View logs
npm run logs
npm run logs:error

# Generate project structure
npm run tree
```

### Adding a New Provider

1. **Create provider directory**:
   ```
   services/providers/newprovider/
   ├── NewProviderSpeechService.js
   ├── NewProviderAIService.js
   └── NewProviderTTSService.js
   ```

2. **Implement interfaces**:
   ```javascript
   // NewProviderAIService.js
   const { IAIService } = require('../../interfaces/IAIService')

   class NewProviderAIService extends IAIService {
     async generateReply(conversationHistory) {
       // Implementation
     }

     async testConnection() {
       // Implementation
     }

     getStatus() {
       // Implementation
     }
   }
   ```

3. **Update ServiceFactory**:
   ```javascript
   // services/factory/ServiceFactory.js
   case 'newprovider':
     services = this.createNewProviderServices(config)
     break
   ```

4. **Add configuration**:
   ```javascript
   // core/config/environment.js
   get newprovider() {
     return {
       apiKey: process.env.NEWPROVIDER_API_KEY,
       endpoint: process.env.NEWPROVIDER_ENDPOINT
     }
   }
   ```

### Debugging

#### Enable Debug Logs
```bash
export LOG_LEVEL=debug
npm run dev
```

#### Monitor Performance
```bash
export ENABLE_METRICS=true
npm run dev
```

#### Test Individual Services
```javascript
// Test script example
const { ServiceFactory } = require('./services/factory/ServiceFactory')

async function testServices() {
  const services = ServiceFactory.create('google')
  const health = await services.testAllConnections()
  console.log('Health check:', health)
}

testServices()
```

## 🚀 Deployment

### Docker Deployment

1. **Build image**:
   ```bash
   docker build -t voice-ai-backend .
   ```

2. **Run container**:
   ```bash
   docker run -p 3001:8080 \
     --env-file .env \
     voice-ai-backend
   ```

### Google Cloud Run

1. **Deploy with Cloud Build**:
   ```bash
   gcloud run deploy voice-ai-backend \
     --source . \
     --platform managed \
     --region europe-west1 \
     --allow-unauthenticated
   ```

2. **Set environment variables**:
   ```bash
   gcloud run services update voice-ai-backend \
     --set-env-vars NODE_ENV=production,API_KEY=your_key
   ```

### Azure Container Apps

1. **Create Container App**:
   ```bash
   az containerapp create \
     --name voice-ai-backend \
     --resource-group voice-ai-rg \
     --environment voice-ai-env \
     --image your-registry.azurecr.io/voice-ai-backend \
     --target-port 8080 \
     --ingress external
   ```

### Kubernetes

```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: voice-ai-backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: voice-ai-backend
  template:
    metadata:
      labels:
        app: voice-ai-backend
    spec:
      containers:
      - name: voice-ai-backend
        image: voice-ai-backend:latest
        ports:
        - containerPort: 8080
        env:
        - name: NODE_ENV
          value: "production"
        - name: API_KEY
          valueFrom:
            secretKeyRef:
              name: voice-ai-secrets
              key: api-key
```

## 🔒 Security

### Authentication
- **API Key Authentication**: All API endpoints require valid API key
- **Socket Authentication**: WebSocket connections require API key
- **CORS Protection**: Configurable allowed origins

### Rate Limiting
```javascript
// Default limits
{
  api: '100 requests/minute/IP',
  socket: '1000 messages/minute/connection',
  connections: '5 connections/minute/IP'
}
```

### Security Headers
- `X-Content-Type-Options: nosniff`
- `X-Frame-Options: DENY`
- `Strict-Transport-Security` (production)
- `Content-Security-Policy`

### Best Practices

1. **Environment Variables**: Never commit sensitive data
2. **API Key Rotation**: Rotate keys regularly
3. **Network Security**: Use HTTPS in production
4. **Monitoring**: Monitor for unusual activity
5. **Updates**: Keep dependencies updated

## 📊 Monitoring & Analytics

### Performance Metrics

Access performance data via API:
```bash
curl -H "X-API-Key: your_api_key" \
     http://localhost:3001/api/metrics
```

**Response:**
```json
{
  "performance": {
    "totalRequests": 1250,
    "averageResponseTime": 285,
    "speechRecognitionTime": 150,
    "aiGenerationTime": 1200,
    "ttsGenerationTime": 800
  },
  "memory": {
    "rss": 120,
    "heapUsed": 85
  },
  "uptime": 7200
}
```

### Logging

Logs are structured JSON with multiple levels:

```javascript
// Log levels: error, warn, info, debug, trace
logger.info('User message processed', {
  sessionId: 'session_123',
  provider: 'google',
  responseTime: 245,
  success: true
});
```

### Health Monitoring

Set up health check endpoints for monitoring systems:

```bash
# Basic health (200 = healthy, 503 = unhealthy)
curl -f http://localhost:3001/health

# Detailed health with service status
curl -H "X-API-Key: your_key" \
     http://localhost:3001/api/health/detailed
```

## 🧪 Testing

### Unit Tests
```bash
npm test
```

### Integration Tests
```bash
# Test specific services
npm run test:services

# Test health endpoints
npm run test:health
```

### Manual Testing
```bash
# Test WebSocket connection
npm run test:socket

# Test audio processing
npm run test:audio

# Load testing
npm run test:load
```

## 🐛 Troubleshooting

### Common Issues

#### 1. Connection Refused
```
Error: connect ECONNREFUSED 127.0.0.1:3001
```
**Solution**: Ensure server is running and port 3001 is available

#### 2. Authentication Failed
```
Error: Invalid API key
```
**Solution**: Check API_KEY in .env file matches client requests

#### 3. Provider Service Unavailable
```
Error: Google AI service quota exceeded
```
**Solution**: Check provider quotas and billing in cloud console

#### 4. Audio Issues
```
Error: Invalid audio buffer format
```
**Solution**: Ensure frontend sends correct audio format (LINEAR16, 16kHz)

### Debug Commands

```bash
# Check service connectivity
node -e "
const { ServiceFactory } = require('./services/factory/ServiceFactory');
ServiceFactory.create('google').testAllConnections()
  .then(r => console.log('Health:', r))
  .catch(e => console.error('Error:', e));
"

# Test environment configuration
node -e "
const { environment } = require('./core/config');
console.log('Config:', environment.getSummary());
"

# Check logs
tail -f logs/app-$(date +%Y-%m-%d).log
```
