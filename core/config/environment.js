/**
 * Centralized environment configuration management
 * Validates and normalizes all environment variables
 */
require('dotenv').config()

class Environment {
  constructor() {
    this.nodeEnv = process.env.NODE_ENV || 'development'
    this.isDevelopment = this.nodeEnv === 'development'
    this.isProduction = this.nodeEnv === 'production'
    this.isTest = this.nodeEnv === 'test'

    // Validate critical environment variables
    this.validateEnvironment()
  }

  // ===========================================
  // SERVER CONFIGURATION
  // ===========================================
  get server() {
    return {
      port: this.isProduction ? 8080 : (process.env.PORT || 3001),
      nodeEnv: this.nodeEnv,
      logLevel: process.env.LOG_LEVEL || (this.isDevelopment ? 'debug' : 'info')
    }
  }

  // ===========================================
  // SECURITY CONFIGURATION
  // ===========================================
  get security() {
    return {
      apiKey: this.getRequired('API_KEY'),
      corsOrigins: this.getCorsOrigins(),
      enableCSRF: process.env.ENABLE_CSRF !== 'false',
      enableRateLimit: process.env.ENABLE_RATE_LIMIT !== 'false',
      sessionTimeout: parseInt(process.env.SESSION_TIMEOUT || '1800000'), // 30 min
      maxRequestSize: process.env.MAX_REQUEST_SIZE || '10mb'
    }
  }

  // ===========================================
  // PROVIDER CONFIGURATION
  // ===========================================
  get providers() {
    return {
      default: process.env.DEFAULT_PROVIDER || this.detectDefaultProvider(),
      enabled: this.getEnabledProviders()
    }
  }

  // ===========================================
  // CUSTOM CONFIGURATION (TUS SERVIDORES)
  // ===========================================
  get custom() {
    return {
      speech: {
        // Configuración básica para stream
        encoding: 'LINEAR16',
        sampleRateHertz: 16000,
        languageCode: 'es-ES',
        bufferProcessing: true,
        streamTimeout: parseInt(process.env.SPEECH_STREAM_TIMEOUT || '60000')
      },
      ai: {
        apiUrl: process.env.IA_API_URL || 'https://dev.dl2discovery.org/llm-api/v1/',
        apiKey: process.env.IA_API_KEY || '9dcd0147-11e2-4e9e-aaf3-05e1498ce828',
        presetIaVsPlayer: process.env.IA_PRESETID_IA_VS_PLAYER || 'mapp-Claude_enygma_V2',
        presetGenCharBot: process.env.IA_PRESETID_GENCHARBOT || 'mapp-gen-char-bot',
        timeout: parseInt(process.env.IA_TIMEOUT || '30000'),
        maxTokens: parseInt(process.env.IA_MAX_TOKENS || '500'),
        temperature: parseFloat(process.env.IA_TEMPERATURE || '0.7')
      },
      tts: {
        apiUrl: process.env.SPEECH_API_URL || 'https://dev.dl2discovery.org/sts/api/v1/',
        apiKey: process.env.SPEECH_API_KEY || 'b075e3a3-3cd8-4b65-ba5e-735e32ec3251',
        defaultVoice: process.env.SPEECH_DEFAULT_VOICE || 'Elvira',
        language: process.env.SPEECH_LANGUAGE || 'es-ES',
        gender: process.env.SPEECH_GENDER || 'female',
        rate: process.env.SPEECH_RATE || '0%',
        outputFormat: process.env.SPEECH_OUTPUT_FORMAT || 'mp3',
        timeout: parseInt(process.env.SPEECH_TIMEOUT || '30000'),
        maxContentLength: parseInt(process.env.SPEECH_MAX_CONTENT || '10485760') // 10MB
      }
    }
  }

  // ===========================================
  // GOOGLE CLOUD CONFIGURATION
  // ===========================================
  get google() {
    return {
      cloud: {
        project: process.env.GOOGLE_CLOUD_PROJECT,
        location: process.env.GOOGLE_CLOUD_LOCATION || 'europe-southwest1',
        keyFile: process.env.GOOGLE_APPLICATION_CREDENTIALS
      },
      speech: {
        encoding: process.env.GOOGLE_SPEECH_ENCODING || 'LINEAR16',
        sampleRate: parseInt(process.env.GOOGLE_SPEECH_SAMPLE_RATE || '16000'),
        language: process.env.GOOGLE_SPEECH_LANGUAGE || 'es-ES',
        model: process.env.GOOGLE_SPEECH_MODEL || 'latest_long',
        useEnhanced: process.env.GOOGLE_SPEECH_ENHANCED !== 'false'
      },
      ai: {
        model: process.env.GOOGLE_AI_MODEL || 'gemini-2.0-flash-001',
        maxTokens: parseInt(process.env.GOOGLE_AI_MAX_TOKENS || '500'),
        temperature: parseFloat(process.env.GOOGLE_AI_TEMPERATURE || '0.7'),
        topP: parseFloat(process.env.GOOGLE_AI_TOP_P || '0.9')
      },
      tts: {
        baseURL: process.env.AUDIO_BACKEND_BASE_URL,
        apiKey: process.env.AUDIO_BACKEND_API_KEY,
        voiceId: process.env.GOOGLE_TTS_VOICE || 'Ximena',
        format: process.env.GOOGLE_TTS_FORMAT || 'mp3',
        timeout: parseInt(process.env.GOOGLE_TTS_TIMEOUT || '30000')
      }
    }
  }

  // ===========================================
  // AZURE CONFIGURATION
  // ===========================================
  get azure() {
    return {
      speech: {
        subscriptionKey: process.env.AZURE_SPEECH_KEY,
        region: process.env.AZURE_SPEECH_REGION || 'westeurope',
        language: process.env.AZURE_SPEECH_LANGUAGE || 'es-ES',
        format: process.env.AZURE_SPEECH_FORMAT || 'Detailed',
        profanity: process.env.AZURE_SPEECH_PROFANITY || 'Masked',
        enableDictation: process.env.AZURE_SPEECH_DICTATION !== 'false'
      },
      openai: {
        endpoint: process.env.AZURE_OPENAI_ENDPOINT,
        apiKey: process.env.AZURE_OPENAI_KEY,
        deploymentName: process.env.AZURE_OPENAI_DEPLOYMENT_NAME || 'gpt-4',
        apiVersion: process.env.AZURE_OPENAI_API_VERSION || '2024-08-01-preview',
        maxTokens: parseInt(process.env.AZURE_OPENAI_MAX_TOKENS || '500'),
        temperature: parseFloat(process.env.AZURE_OPENAI_TEMPERATURE || '0.7')
      },
      tts: {
        subscriptionKey: process.env.AZURE_SPEECH_KEY,
        region: process.env.AZURE_SPEECH_REGION || 'westeurope',
        voice: process.env.AZURE_TTS_VOICE || 'es-ES-ElviraNeural',
        format: process.env.AZURE_TTS_FORMAT || 'Audio16Khz32KBitRateMonoMp3',
        speakingRate: process.env.AZURE_TTS_RATE || '0%',
        pitch: process.env.AZURE_TTS_PITCH || '0%'
      }
    }
  }

  // ===========================================
  // AI CONFIGURATION
  // ===========================================
  get ai() {
    return {
      systemPrompt: process.env.AI_PROMPT || this.getDefaultPrompt(),
      maxInputLength: parseInt(process.env.MAX_USER_INPUT_LENGTH || '1000'),
      maxConversationHistory: parseInt(process.env.MAX_CONVERSATION_HISTORY || '50'),
      enableCaching: process.env.AI_ENABLE_CACHING !== 'false',
      responseTimeout: parseInt(process.env.AI_RESPONSE_TIMEOUT || '30000')
    }
  }

  // ===========================================
  // PERFORMANCE CONFIGURATION
  // ===========================================
  get performance() {
    return {
      enableMetrics: process.env.ENABLE_METRICS === 'true',
      socketTimeout: parseInt(process.env.SOCKET_TIMEOUT || '30000'),
      audioTimeout: parseInt(process.env.AUDIO_TIMEOUT || '60000'),
      maxAudioBufferSize: parseInt(process.env.MAX_AUDIO_BUFFER_SIZE || '1048576'), // 1MB
      enableCompression: process.env.ENABLE_COMPRESSION !== 'false'
    }
  }

  // ===========================================
  // MONITORING & LOGGING
  // ===========================================
  get monitoring() {
    return {
      enableHealthChecks: process.env.ENABLE_HEALTH_CHECKS !== 'false',
      healthCheckInterval: parseInt(process.env.HEALTH_CHECK_INTERVAL || '30000'),
      enableDetailedLogs: process.env.ENABLE_DETAILED_LOGS === 'true',
      logToFile: process.env.LOG_TO_FILE === 'true',
      logDirectory: process.env.LOG_DIRECTORY || './logs'
    }
  }

  // ===========================================
  // HELPER METHODS
  // ===========================================

  getRequired(key) {
    const value = process.env[key]
    if (!value) {
      throw new Error(`Required environment variable ${key} is not set`)
    }
    return value
  }

  getOptional(key, defaultValue = null) {
    return process.env[key] || defaultValue
  }

  getCorsOrigins() {
    if (this.isProduction) {
      const origins = []
      if (process.env.FRONTEND_PRO_URL) origins.push(process.env.FRONTEND_PRO_URL)
      if (process.env.FRONTEND_STAGING_URL) origins.push(process.env.FRONTEND_STAGING_URL)
      return origins.length ? origins : ['https://yourdomain.com']
    }

    // Development origins
    return [
      process.env.FRONTEND_DEV_URL || 'http://localhost:3000',
      'http://localhost:5173', // Vite
      'http://localhost:4200', // Angular
      'http://127.0.0.1:3000'
    ]
  }

  detectDefaultProvider() {
    // Auto-detect based on available credentials
    const hasCustom = process.env.IA_API_URL && process.env.SPEECH_API_URL
    const hasGoogle = process.env.GOOGLE_CLOUD_PROJECT && process.env.AUDIO_BACKEND_BASE_URL
    const hasAzure = process.env.AZURE_SPEECH_KEY && process.env.AZURE_OPENAI_ENDPOINT

    if (hasCustom) {
      console.log('🔧 Custom provider detected, using as default')
      return 'custom'
    } else if (hasGoogle && hasAzure) {
      console.log('🔍 Both Google and Azure detected, using Google as default')
      return 'google'
    } else if (hasAzure) {
      return 'azure'
    } else if (hasGoogle) {
      return 'google'
    }

    throw new Error('No valid provider configuration detected. Please configure Custom, Google, or Azure credentials.')
  }

  getEnabledProviders() {
    const providers = []

    // Check Custom (TUS SERVIDORES)
    if (process.env.IA_API_URL && process.env.SPEECH_API_URL) {
      providers.push('custom')
    }

    // Check Google
    if (process.env.GOOGLE_CLOUD_PROJECT && process.env.AUDIO_BACKEND_BASE_URL) {
      providers.push('google')
    }

    // Check Azure
    if (process.env.AZURE_SPEECH_KEY && process.env.AZURE_OPENAI_ENDPOINT) {
      providers.push('azure')
    }

    return providers
  }

  validateEnvironment() {
    const errors = []

    // Validate API key
    if (!process.env.API_KEY) {
      errors.push('API_KEY is required for security')
    }

    // Validate at least one provider
    const enabledProviders = this.getEnabledProviders()
    if (enabledProviders.length === 0) {
      errors.push('At least one provider (Custom, Google or Azure) must be configured')
    }

    // Validate Custom if enabled
    if (enabledProviders.includes('custom')) {
      if (!process.env.IA_API_URL) {
        errors.push('IA_API_URL is required for Custom AI provider')
      }
      if (!process.env.IA_API_KEY) {
        errors.push('IA_API_KEY is required for Custom AI provider')
      }
      if (!process.env.SPEECH_API_URL) {
        errors.push('SPEECH_API_URL is required for Custom TTS provider')
      }
      if (!process.env.SPEECH_API_KEY) {
        errors.push('SPEECH_API_KEY is required for Custom TTS provider')
      }
    }

    // Validate Google if enabled
    if (enabledProviders.includes('google')) {
      if (!process.env.GOOGLE_CLOUD_PROJECT) {
        errors.push('GOOGLE_CLOUD_PROJECT is required for Google provider')
      }
      if (!process.env.AUDIO_BACKEND_BASE_URL) {
        errors.push('AUDIO_BACKEND_BASE_URL is required for Google TTS')
      }
    }

    // Validate Azure if enabled
    if (enabledProviders.includes('azure')) {
      if (!process.env.AZURE_SPEECH_KEY) {
        errors.push('AZURE_SPEECH_KEY is required for Azure provider')
      }
      if (!process.env.AZURE_OPENAI_ENDPOINT) {
        errors.push('AZURE_OPENAI_ENDPOINT is required for Azure OpenAI')
      }
    }

    if (errors.length > 0) {
      throw new Error(`Environment validation failed:\n${errors.map(e => `  - ${e}`).join('\n')}`)
    }
  }

  getDefaultPrompt() {
    return `**Descripción**
Eres Enygma, un juego de adivinanza que piensa en un personaje reconocido y famoso del entretenimiento. El usuario tiene hasta 20 preguntas para descubrir quién es.

Todas las respuestas de Enygma se deben entregar en formato JSON con la siguiente estructura:
{
  "respuesta": "<frase breve basada en 'Sí', 'No' o 'No lo sé' con tono narrativo>",
  "pista": "<breve pista de 2—4 palabras>",
  "acertado": true|false,
  "cuenta_regresiva": <número de preguntas restantes>,
  "juego_finalizado": true|false
}

**Inicio del juego**
Cuando todo esté listo, responde exactamente (en texto plano):
Ya estoy pensando en un personaje del mundo del entretenimiento. Puedes empezar a hacer preguntas. Solo responderé con frases derivadas de "sí", "no" o "no lo sé".`
  }

  // ===========================================
  // SUMMARY & DEBUG
  // ===========================================

  getSummary() {
    return {
      environment: this.nodeEnv,
      server: {
        port: this.server.port,
        logLevel: this.server.logLevel
      },
      providers: {
        default: this.providers.default,
        enabled: this.providers.enabled
      },
      security: {
        corsOrigins: this.security.corsOrigins.length,
        enableCSRF: this.security.enableCSRF,
        enableRateLimit: this.security.enableRateLimit
      },
      custom: {
        configured: !!(this.custom.ai.apiUrl && this.custom.tts.apiUrl),
        aiUrl: this.custom.ai.apiUrl,
        ttsUrl: this.custom.tts.apiUrl,
        presets: {
          iaVsPlayer: this.custom.ai.presetIaVsPlayer,
          genCharBot: this.custom.ai.presetGenCharBot
        }
      },
      google: {
        configured: !!(this.google.cloud.project && this.google.tts.baseURL),
        project: this.google.cloud.project,
        speechModel: this.google.speech.model,
        aiModel: this.google.ai.model
      },
      azure: {
        configured: !!(this.azure.speech.subscriptionKey && this.azure.openai.endpoint),
        region: this.azure.speech.region,
        deployment: this.azure.openai.deploymentName
      }
    }
  }

  exportSafeConfig() {
    const config = this.getSummary()

    // Remove sensitive info for logging
    return JSON.stringify(config, null, 2)
  }
}

// Singleton instance
const environment = new Environment()

module.exports = { Environment, environment }
