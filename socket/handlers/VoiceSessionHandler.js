/**
 * Voice session socket handler
 * Manages real-time voice communication
 */
const { ServiceFactory } = require('../../services/factory/ServiceFactory')
const { validation, logger, performance } = require('../../core/utils')
const { SOCKET_EVENTS, ERROR_CODES } = require('../../core/types/voice')

class VoiceSessionHandler {
  constructor(socket, options = {}) {
    this.socket = socket
    this.sessionId = socket.id
    this.provider = options.provider || 'google'
    this.services = null
    this.conversationHistory = []
    this.isMuted = false
    this.isStreamActive = false
    this.recognizeStream = null
    this.inactivityTimeout = null
    
    this.initialize()
  }

  async initialize() {
    try {
      // Create services for this session
      this.services = ServiceFactory.create(this.provider)
      
      // Test connections
      const health = await this.services.testAllConnections()
      if (!health.allHealthy) {
        logger.warn(`Services not fully healthy for session ${this.sessionId}:`, health)
      }

      // Setup event handlers
      this.setupEventHandlers()
      
      // Start recognition stream
      this.startRecognitionStream()
      
      // Setup inactivity timeout
      this.resetInactivityTimer()
      
      logger.info(`Voice session initialized: ${this.sessionId} with provider: ${this.provider}`)
      
    } catch (error) {
      logger.error(`Failed to initialize voice session ${this.sessionId}:`, error)
      this.emitError('SESSION_INITIALIZATION_FAILED', 'Failed to initialize voice session')
    }
  }

  setupEventHandlers() {
    // Audio data from client
    this.socket.on(SOCKET_EVENTS.AUDIO_DATA, (audioBuffer) => {
      this.handleAudioData(audioBuffer)
    })

    // Mute/unmute controls
    this.socket.on(SOCKET_EVENTS.MUTE_SOUND, () => {
      this.handleMute()
    })

    this.socket.on(SOCKET_EVENTS.UNMUTE_SOUND, () => {
      this.handleUnmute()
    })

    // Session control
    this.socket.on(SOCKET_EVENTS.END_SESSION, () => {
      this.handleEndSession()
    })

    // Connection events
    this.socket.on('disconnect', (reason) => {
      this.handleDisconnect(reason)
    })

    // Ping/pong for keep-alive
    this.socket.on(SOCKET_EVENTS.PING, () => {
      this.socket.emit(SOCKET_EVENTS.PONG)
    })
  }

  async handleAudioData(audioBuffer) {
    const timer = performance.startTimer('audio_processing')
    
    try {
      // Validate audio buffer
      const validation_result = validation.validateAudioBuffer(audioBuffer)
      if (!validation_result.valid) {
        this.emitError(ERROR_CODES.INVALID_AUDIO_BUFFER, validation_result.errors.join(', '))
        return
      }

      // Ensure recognition stream is active
      if (!this.recognizeStream || !this.isStreamActive) {
        this.startRecognitionStream()
      }

      // Write audio data to recognition stream
      this.services.speech.writeAudioData(audioBuffer)
      
      // Reset inactivity timer
      this.resetInactivityTimer()
      
    } catch (error) {
      logger.error(`Audio processing failed for session ${this.sessionId}:`, error)
      this.emitError(ERROR_CODES.SERVICE_UNAVAILABLE, 'Audio processing temporarily unavailable')
    } finally {
      performance.endTimer('audio_processing')
    }
  }

  startRecognitionStream() {
    try {
      // Clean up existing stream
      this.cleanupRecognitionStream()
      
      logger.debug(`Starting recognition stream for session: ${this.sessionId}`)
      this.isStreamActive = true

      // Create new recognition stream
      this.recognizeStream = this.services.speech.createRecognizer(
        (result) => this.handleSpeechResult(result),
        (error) => this.handleSpeechError(error),
        () => this.handleStreamEnd()
      )

      this.services.speech.startContinuousRecognition()
      
    } catch (error) {
      logger.error(`Failed to start recognition stream for session ${this.sessionId}:`, error)
      this.isStreamActive = false
      this.emitError(ERROR_CODES.SERVICE_UNAVAILABLE, 'Speech recognition temporarily unavailable')
    }
  }

  async handleSpeechResult(result) {
    const timer = performance.startTimer('speech_result_processing')
    
    try {
      // Emit transcription to client
      this.socket.emit(SOCKET_EVENTS.TRANSCRIPTION, result.text)

      // Process final results
      if (result.isFinal && result.text && result.text.trim() !== '') {
        await this.processFinalTranscription(result.text.trim())
      }
      
    } catch (error) {
      logger.error(`Speech result processing failed for session ${this.sessionId}:`, error)
    } finally {
      performance.endTimer('speech_result_processing')
    }
  }

  async processFinalTranscription(transcription) {
    const timer = performance.startTimer('ai_processing')
    
    try {
      // Show loading indicator
      this.socket.emit(SOCKET_EVENTS.LOADING)

      // Handle special commands
      if (transcription.toLowerCase() === 'ok aura') {
        this.socket.emit(SOCKET_EVENTS.REPLY, { text: '[OK AURA]', audioUrl: null })
        return
      }

      // Clean transcription
      let cleanText = transcription
      if (cleanText.toLowerCase().startsWith('ok aura')) {
        cleanText = cleanText.slice(7).trim()
      }

      // Validate and add to conversation history
      const textValidation = validation.validateTextInput(cleanText)
      if (!textValidation.valid) {
        this.emitError(ERROR_CODES.INVALID_TEXT_INPUT, textValidation.errors.join(', '))
        return
      }

      this.conversationHistory.push({ user: true, content: cleanText })
      logger.info(`User (${this.sessionId}): ${cleanText}`)

      // Generate AI response
      const aiReply = await this.services.ai.generateReply(this.conversationHistory)
      if (!aiReply) {
        throw new Error('AI service returned empty response')
      }

      logger.info(`AI (${this.sessionId}): ${aiReply}`)
      this.conversationHistory.push({ user: false, content: aiReply })

      // Handle special response types
      if (this.isSpecialResponse(aiReply)) {
        this.socket.emit(SOCKET_EVENTS.REPLY, { text: aiReply, audioUrl: null })
        return
      }

      // Generate speech if not muted
      let audioUrl = null
      if (!this.isMuted) {
        audioUrl = await this.generateSpeechAudio(aiReply)
      }

      // Send response to client
      this.socket.emit(SOCKET_EVENTS.REPLY, { text: aiReply, audioUrl })
      
    } catch (error) {
      logger.error(`AI processing failed for session ${this.sessionId}:`, error)
      this.emitError(ERROR_CODES.SERVICE_UNAVAILABLE, 'Error processing your request')
    } finally {
      performance.endTimer('ai_processing')
    }
  }

  async generateSpeechAudio(text) {
    const timer = performance.startTimer('tts_generation')
    
    try {
      const audioBuffer = await this.services.tts.generateSpeech(text)
      
      if (audioBuffer && audioBuffer.length > 0) {
        return `data:audio/mpeg;base64,${audioBuffer.toString('base64')}`
      }
      
      return null
      
    } catch (error) {
      logger.warn(`TTS generation failed for session ${this.sessionId}:`, error.message)
      return null // Continue without audio
    } finally {
      performance.endTimer('tts_generation')
    }
  }

  isSpecialResponse(response) {
    const specialPrefixes = ['[JSON]', '[NONE]', '[COMMAND]', '[PAUSE]', '[EXIT]']
    return specialPrefixes.some(prefix => response.startsWith(prefix))
  }

  handleSpeechError(error) {
    logger.error(`Speech recognition error for session ${this.sessionId}:`, error)
    this.isStreamActive = false
    this.cleanupRecognitionStream()
    this.emitError(ERROR_CODES.SERVICE_UNAVAILABLE, 'Speech recognition temporarily unavailable')
  }

  handleStreamEnd() {
    logger.debug(`Speech recognition stream ended for session: ${this.sessionId}`)
    this.isStreamActive = false
  }

  handleMute() {
    this.isMuted = true
    logger.debug(`Session ${this.sessionId}: Sound muted`)
  }

  handleUnmute() {
    this.isMuted = false
    logger.debug(`Session ${this.sessionId}: Sound unmuted`)
  }

  handleEndSession() {
    logger.info(`Session ended by client: ${this.sessionId}`)
    this.cleanup()
  }

  handleDisconnect(reason) {
    logger.info(`Session disconnected: ${this.sessionId}, reason: ${reason}`)
    this.cleanup()
  }

  resetInactivityTimer() {
    if (this.inactivityTimeout) {
      clearTimeout(this.inactivityTimeout)
    }

    this.inactivityTimeout = setTimeout(() => {
      logger.info(`Session ${this.sessionId}: Closing due to inactivity`)
      this.socket.disconnect(true)
    }, 60000) // 1 minute
  }

  cleanupRecognitionStream() {
    if (this.recognizeStream) {
      try {
        this.services.speech.cleanup()
        this.recognizeStream = null
      } catch (error) {
        logger.error(`Error cleaning up recognition stream for session ${this.sessionId}:`, error)
      }
    }
  }

  cleanup() {
    try {
      // Clear timers
      if (this.inactivityTimeout) {
        clearTimeout(this.inactivityTimeout)
        this.inactivityTimeout = null
      }

      // Cleanup recognition stream
      this.cleanupRecognitionStream()

      // Cleanup services
      if (this.services?.cleanupAll) {
        this.services.cleanupAll()
      }

      // Clear conversation history
      this.conversationHistory = []

      logger.debug(`Session ${this.sessionId}: Cleanup completed`)
      
    } catch (error) {
      logger.error(`Error during cleanup for session ${this.sessionId}:`, error)
    }
  }

  emitError(code, message) {
    this.socket.emit(SOCKET_EVENTS.ERROR, {
      code,
      message,
      timestamp: new Date().toISOString()
    })
  }

  getSessionStatus() {
    return {
      sessionId: this.sessionId,
      provider: this.provider,
      isActive: this.isStreamActive,
      isMuted: this.isMuted,
      conversationTurns: this.conversationHistory.length,
      uptime: Date.now() - this.socket.user.connectionTime
    }
  }
}

module.exports = { VoiceSessionHandler }