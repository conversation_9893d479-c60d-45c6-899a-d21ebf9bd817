/**
 * API Routes configuration
 * Modular route setup with controllers
 */
const express = require('express')
const { createRateLimiter } = require('../../core/config/security')

/**
 * Create API routes with dependency injection
 */
function createApiRoutes(controllers) {
  const router = express.Router()
  const { healthController } = controllers

  // Apply rate limiting to all API routes
  router.use(createRateLimiter({
    windowMs: 60000, // 1 minute
    max: 100 // requests per window per IP
  }))

  // Health routes
  router.get('/health', (req, res) => healthController.getHealth(req, res))
  router.get('/health/detailed', (req, res) => healthController.getDetailedHealth(req, res))
  router.get('/health/provider/:provider', (req, res) => healthController.getProviderStatus(req, res))
  router.get('/metrics', (req, res) => healthController.getMetrics(req, res))

  // Service routes
  router.use('/services', createServiceRoutes())

  // Configuration routes
  router.use('/config', createConfigRoutes())

  // Session management routes
  router.use('/sessions', createSessionRoutes())

  return router
}

/**
 * Service management routes
 */
function createServiceRoutes() {
  const router = express.Router()
  const { ServiceFactory } = require('../../services/factory/ServiceFactory')
  const { logger } = require('../../core/utils')

  // Test provider services
  router.post('/test/:provider', async (req, res) => {
    try {
      const { provider } = req.params
      
      if (!['google', 'azure'].includes(provider)) {
        return res.status(400).json({
          error: 'Invalid provider. Must be "google" or "azure"'
        })
      }

      const services = ServiceFactory.create(provider)
      const results = await services.testAllConnections()

      res.json({
        provider,
        status,
        timestamp: new Date().toISOString()
      })

    } catch (error) {
      logger.error('Service status failed:', error)
      res.status(500).json({
        error: error.message
      })
    }
  })

  // Get available providers
  router.get('/providers', (req, res) => {
    const { environment } = require('../../core/config')
    
    res.json({
      available: ServiceFactory.getSupportedProviders(),
      enabled: environment.providers.enabled,
      default: environment.providers.default
    })
  })

  return router
}

/**
 * Configuration management routes
 */
function createConfigRoutes() {
  const router = express.Router()
  const { configManager } = require('../../services/factory/ConfigManager')
  const { environment } = require('../../core/config')
  const { logger } = require('../../core/utils')

  // Get configuration summary
  router.get('/summary/:provider?', (req, res) => {
    try {
      const provider = req.params.provider || environment.providers.default
      const summary = configManager.getConfigSummary(provider)

      res.json({
        provider,
        summary,
        timestamp: new Date().toISOString()
      })

    } catch (error) {
      logger.error('Config summary failed:', error)
      res.status(500).json({
        error: error.message
      })
    }
  })

  // Get available voices for TTS
  router.get('/voices/:provider?', (req, res) => {
    try {
      const provider = req.params.provider || environment.providers.default
      const voices = configManager.getAvailableVoices(provider)

      res.json({
        provider,
        voices
      })

    } catch (error) {
      logger.error('Get voices failed:', error)
      res.status(500).json({
        error: error.message
      })
    }
  })

  // Get available AI models
  router.get('/models/:provider?', (req, res) => {
    try {
      const provider = req.params.provider || environment.providers.default
      const models = configManager.getAvailableModels(provider)

      res.json({
        provider,
        models
      })

    } catch (error) {
      logger.error('Get models failed:', error)
      res.status(500).json({
        error: error.message
      })
    }
  })

  return router
}

/**
 * Session management routes
 */
function createSessionRoutes() {
  const router = express.Router()
  const { logger } = require('../../core/utils')

  // Get active sessions (requires server reference)
  router.get('/active', (req, res) => {
    try {
      // This would need to be injected from the main server
      // For now, return empty array
      res.json({
        sessions: [],
        count: 0,
        message: 'Session tracking not yet implemented'
      })

    } catch (error) {
      logger.error('Get active sessions failed:', error)
      res.status(500).json({
        error: error.message
      })
    }
  })

  // Get session statistics
  router.get('/stats', (req, res) => {
    try {
      const { performance } = require('../../core/utils')
      const stats = performance.getReport()

      res.json({
        performance: stats,
        timestamp: new Date().toISOString()
      })

    } catch (error) {
      logger.error('Get session stats failed:', error)
      res.status(500).json({
        error: error.message
      })
    }
  })

  return router
}

/**
 * Create API middleware
 */
function createApiMiddleware(app) {
  const { logger } = require('../../core/utils')

  // Request logging middleware
  app.use('/api', (req, res, next) => {
    const start = Date.now()
    
    res.on('finish', () => {
      const duration = Date.now() - start
      logger.logRequest(req, res, duration)
    })
    
    next()
  })

  // JSON parsing with error handling
  app.use('/api', (error, req, res, next) => {
    if (error instanceof SyntaxError && error.status === 400 && 'body' in error) {
      logger.warn('Invalid JSON in request:', error.message)
      return res.status(400).json({
        error: 'Invalid JSON format',
        code: 'INVALID_JSON'
      })
    }
    next(error)
  })
}

module.exports = {
  createApiRoutes,
  createApiMiddleware,
  createServiceRoutes,
  createConfigRoutes,
  createSessionRoutes
}json({
        provider,
        results,
        timestamp: new Date().toISOString()
      })

    } catch (error) {
      logger.error('Service test failed:', error)
      res.status(500).json({
        error: error.message,
        provider: req.params.provider
      })
    }
  })

  // Get service status
  router.get('/status/:provider?', async (req, res) => {
    try {
      const provider = req.params.provider || 'google'
      
      const services = ServiceFactory.create(provider)
      const status = services.getAllStatuses()

      res.