/**
 * Health check controller
 * Provides system health endpoints
 */
const { ServiceFactory } = require('../../services/factory/ServiceFactory')
const { configManager } = require('../../services/factory/ConfigManager')
const { performance, logger } = require('../../core/utils')

class HealthController {
  constructor() {
    this.services = null
  }

  /**
   * Basic health check
   */
  async getHealth(req, res) {
    try {
      const healthData = {
        status: 'ok',
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV,
        uptime: Math.floor(process.uptime()),
        memory: this.getMemoryInfo(),
        version: process.env.npm_package_version || '1.0.0'
      }

      res.json(healthData)
    } catch (error) {
      logger.error('Health check failed:', error)
      res.status(500).json({
        status: 'error',
        timestamp: new Date().toISOString(),
        error: 'Health check failed'
      })
    }
  }

  /**
   * Detailed health check with services
   */
  async getDetailedHealth(req, res) {
    try {
      const startTime = performance.startTimer('health_check')
      
      // Get or create services
      if (!this.services) {
        const provider = req.query.provider || 'google'
        this.services = ServiceFactory.createFromEnv(provider)
      }

      // Test all services
      const serviceHealth = await this.services.testAllConnections()
      
      const healthData = {
        status: serviceHealth.allHealthy ? 'ok' : 'degraded',
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV,
        provider: serviceHealth.provider,
        uptime: Math.floor(process.uptime()),
        memory: this.getMemoryInfo(),
        services: {
          speech: {
            healthy: serviceHealth.services.speech,
            provider: serviceHealth.provider
          },
          ai: {
            healthy: serviceHealth.services.ai,
            provider: serviceHealth.provider
          },
          tts: {
            healthy: serviceHealth.services.tts,
            provider: serviceHealth.provider
          }
        },
        configuration: configManager.getConfigSummary(serviceHealth.provider),
        performance: performance.getReport()
      }

      performance.endTimer('health_check')
      
      const statusCode = serviceHealth.allHealthy ? 200 : 503
      res.status(statusCode).json(healthData)

    } catch (error) {
      logger.error('Detailed health check failed:', error)
      res.status(500).json({
        status: 'error',
        timestamp: new Date().toISOString(),
        error: error.message
      })
    }
  }

  /**
   * Provider status endpoint
   */
  async getProviderStatus(req, res) {
    try {
      const { provider = 'google' } = req.params
      
      if (!['google', 'azure'].includes(provider)) {
        return res.status(400).json({
          error: 'Invalid provider. Must be "google" or "azure"'
        })
      }

      const services = ServiceFactory.create(provider)
      const status = services.getAllStatuses()
      const health = await services.testAllConnections()

      res.json({
        provider,
        status,
        health,
        timestamp: new Date().toISOString()
      })

    } catch (error) {
      logger.error(`Provider ${req.params.provider} status check failed:`, error)
      res.status(500).json({
        error: error.message,
        provider: req.params.provider
      })
    }
  }

  /**
   * Performance metrics endpoint
   */
  getMetrics(req, res) {
    try {
      const metrics = {
        performance: performance.getReport(),
        memory: this.getMemoryInfo(),
        uptime: Math.floor(process.uptime()),
        timestamp: new Date().toISOString()
      }

      res.json(metrics)
    } catch (error) {
      logger.error('Metrics retrieval failed:', error)
      res.status(500).json({
        error: 'Failed to retrieve metrics'
      })
    }
  }

  getMemoryInfo() {
    const usage = process.memoryUsage()
    return {
      rss: Math.round(usage.rss / 1024 / 1024),
      heapTotal: Math.round(usage.heapTotal / 1024 / 1024),
      heapUsed: Math.round(usage.heapUsed / 1024 / 1024),
      external: Math.round(usage.external / 1024 / 1024),
      unit: 'MB'
    }
  }
}

module.exports = { HealthController }