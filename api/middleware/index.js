/**
 * API Middleware configuration
 * Request validation, logging, and error handling for REST endpoints
 */
const { logger, validation, performance } = require('../../core/utils')
const { securityManager } = require('../../core/config')

/**
 * Create API middleware stack
 */
function createApiMiddleware(app) {
  logger.debug('⚙️ Setting up API middleware...')

  // Request logging middleware
  app.use('/api', requestLoggingMiddleware)

  // Request validation middleware
  app.use('/api', requestValidationMiddleware)

  // Performance monitoring middleware
  app.use('/api', performanceMiddleware)

  // JSON parsing with error handling
  app.use('/api', jsonParsingMiddleware)

  // Security headers for API
  app.use('/api', apiSecurityMiddleware)

  logger.debug('✅ API middleware configured')
}

/**
 * Request logging middleware
 */
function requestLoggingMiddleware(req, res, next) {
  const start = Date.now()
  const requestId = generateRequestId()

  // Add request ID to headers and request object
  req.requestId = requestId
  res.setHeader('X-Request-ID', requestId)

  // Create request logger
  req.logger = logger.child({
    requestId,
    method: req.method,
    url: req.url,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  })

  req.logger.info('📡 API Request started')

  // Log response when finished
  res.on('finish', () => {
    const duration = Date.now() - start

    const logData = {
      method: req.method,
      url: req.url,
      status: res.statusCode,
      responseTime: `${duration}ms`,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      contentLength: res.get('Content-Length') || 0,
      requestId
    }

    if (res.statusCode >= 400) {
      req.logger.warn(`📡 API Request completed with error`, logData)
    } else {
      req.logger.info(`📡 API Request completed successfully`, logData)
    }

    // Record performance metrics
    performance.recordMetric(`api_request_${req.method}_${req.route?.path || req.url}`, duration, {
      status: res.statusCode,
      success: res.statusCode < 400
    })
  })

  next()
}

/**
 * Request validation middleware
 */
function requestValidationMiddleware(req, res, next) {
  try {
    // Validate request headers
    const headerValidation = validation.validateRequestHeaders(req.headers)
    if (!headerValidation.valid) {
      req.logger.warn('📝 Invalid request headers:', headerValidation.errors)
      return res.status(400).json({
        error: 'Invalid request headers',
        details: headerValidation.errors,
        code: 'INVALID_HEADERS'
      })
    }

    // Log warnings for header issues
    if (headerValidation.warnings?.length > 0) {
      req.logger.debug('⚠️ Request header warnings:', headerValidation.warnings)
    }

    // Validate Content-Type for POST/PUT requests
    if (['POST', 'PUT', 'PATCH'].includes(req.method)) {
      const contentType = req.get('Content-Type')
      if (contentType && !contentType.includes('application/json')) {
        req.logger.warn('📝 Unexpected Content-Type:', contentType)
        return res.status(415).json({
          error: 'Unsupported Media Type',
          expected: 'application/json',
          received: contentType,
          code: 'UNSUPPORTED_MEDIA_TYPE'
        })
      }
    }

    // Validate request size
    const contentLength = req.get('Content-Length')
    if (contentLength) {
      const maxSize = 10 * 1024 * 1024 // 10MB
      if (parseInt(contentLength) > maxSize) {
        req.logger.warn('📝 Request too large:', contentLength)
        return res.status(413).json({
          error: 'Request entity too large',
          maxSize: `${maxSize / 1024 / 1024}MB`,
          received: `${Math.round(contentLength / 1024 / 1024 * 100) / 100}MB`,
          code: 'REQUEST_TOO_LARGE'
        })
      }
    }

    next()

  } catch (error) {
    req.logger.error('📝 Request validation error:', error)
    res.status(500).json({
      error: 'Request validation failed',
      code: 'VALIDATION_ERROR'
    })
  }
}

/**
 * Performance monitoring middleware
 */
function performanceMiddleware(req, res, next) {
  // Start performance timer for this request
  const timerName = `api_${req.method}_${req.url.replace(/[^a-zA-Z0-9]/g, '_')}`
  performance.startTimer(timerName)

  // End timer when response finishes
  res.on('finish', () => {
    performance.endTimer(timerName)
  })

  // Add performance utilities to request
  req.performance = {
    startTimer: (name) => performance.startTimer(`${req.requestId}_${name}`),
    endTimer: (name) => performance.endTimer(`${req.requestId}_${name}`),
    recordMetric: (name, value, metadata) => performance.recordMetric(name, value, {
      ...metadata,
      requestId: req.requestId
    })
  }

  next()
}

/**
 * JSON parsing middleware with error handling
 */
function jsonParsingMiddleware(error, req, res, next) {
  if (error instanceof SyntaxError && error.status === 400 && 'body' in error) {
    req.logger?.warn('📝 Invalid JSON in request body:', error.message)
    return res.status(400).json({
      error: 'Invalid JSON format',
      details: error.message,
      code: 'INVALID_JSON'
    })
  }

  if (error.type === 'entity.too.large') {
    req.logger?.warn('📝 Request body too large')
    return res.status(413).json({
      error: 'Request body too large',
      code: 'BODY_TOO_LARGE'
    })
  }

  next(error)
}

/**
 * Security headers for API endpoints
 */
function apiSecurityMiddleware(req, res, next) {
  // API-specific security headers
  res.setHeader('X-Content-Type-Options', 'nosniff')
  res.setHeader('X-Frame-Options', 'DENY')
  res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate')
  res.setHeader('Pragma', 'no-cache')

  // Rate limiting info (if available)
  if (req.rateLimit) {
    res.setHeader('X-RateLimit-Limit', req.rateLimit.limit)
    res.setHeader('X-RateLimit-Remaining', req.rateLimit.remaining)
    res.setHeader('X-RateLimit-Reset', req.rateLimit.reset)
  }

  next()
}

/**
 * Provider validation middleware
 */
function validateProviderMiddleware(req, res, next) {
  const provider = req.params.provider || req.query.provider || req.body?.provider

  if (provider && !['google', 'azure'].includes(provider)) {
    req.logger?.warn('📝 Invalid provider parameter:', provider)
    return res.status(400).json({
      error: 'Invalid provider',
      validProviders: ['google', 'azure'],
      received: provider,
      code: 'INVALID_PROVIDER'
    })
  }

  // Add validated provider to request
  req.validatedProvider = provider

  next()
}

/**
 * Query parameter validation middleware
 */
function validateQueryParams(allowedParams = []) {
  return (req, res, next) => {
    const queryKeys = Object.keys(req.query)
    const invalidParams = queryKeys.filter(key => !allowedParams.includes(key))

    if (invalidParams.length > 0) {
      req.logger?.warn('📝 Invalid query parameters:', invalidParams)
      return res.status(400).json({
        error: 'Invalid query parameters',
        invalidParams,
        allowedParams,
        code: 'INVALID_QUERY_PARAMS'
      })
    }

    next()
  }
}

/**
 * Body validation middleware factory
 */
function validateBody(schema) {
  return (req, res, next) => {
    if (!req.body) {
      return res.status(400).json({
        error: 'Request body is required',
        code: 'MISSING_BODY'
      })
    }

    // Simple schema validation (you could use Joi or similar here)
    const errors = []

    for (const [field, rules] of Object.entries(schema)) {
      const value = req.body[field]

      if (rules.required && (value === undefined || value === null)) {
        errors.push(`Field '${field}' is required`)
      }

      if (value !== undefined && rules.type && typeof value !== rules.type) {
        errors.push(`Field '${field}' must be of type ${rules.type}`)
      }

      if (value && rules.minLength && value.length < rules.minLength) {
        errors.push(`Field '${field}' must be at least ${rules.minLength} characters`)
      }

      if (value && rules.maxLength && value.length > rules.maxLength) {
        errors.push(`Field '${field}' must be at most ${rules.maxLength} characters`)
      }
    }

    if (errors.length > 0) {
      req.logger?.warn('📝 Body validation errors:', errors)
      return res.status(400).json({
        error: 'Body validation failed',
        details: errors,
        code: 'INVALID_BODY'
      })
    }

    next()
  }
}

/**
 * Error handling middleware for API
 */
function apiErrorHandler(error, req, res, next) {
  req.logger?.error('📝 API Error:', {
    error: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method
  })

  // Determine error type and status
  let status = error.status || error.statusCode || 500
  let code = error.code || 'INTERNAL_ERROR'
  let message = error.message || 'Internal server error'

  // Handle specific error types
  if (error.name === 'ValidationError') {
    status = 400
    code = 'VALIDATION_ERROR'
  } else if (error.name === 'UnauthorizedError') {
    status = 401
    code = 'UNAUTHORIZED'
  } else if (error.name === 'ForbiddenError') {
    status = 403
    code = 'FORBIDDEN'
  } else if (error.name === 'NotFoundError') {
    status = 404
    code = 'NOT_FOUND'
  }

  // Don't expose internal errors in production
  if (status === 500 && process.env.NODE_ENV === 'production') {
    message = 'Internal server error'
  }

  res.status(status).json({
    error: message,
    code,
    requestId: req.requestId,
    timestamp: new Date().toISOString()
  })
}

/**
 * Generate unique request ID
 */
function generateRequestId() {
  return `req_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`
}

/**
 * 404 handler for unmatched API routes
 */
function notFoundHandler(req, res) {
  req.logger?.warn('📝 API route not found:', {
    method: req.method,
    url: req.url
  })

  res.status(404).json({
    error: 'API endpoint not found',
    path: req.originalUrl,
    method: req.method,
    code: 'ENDPOINT_NOT_FOUND',
    requestId: req.requestId,
    availableEndpoints: [
      'GET /api/health',
      'GET /api/health/detailed',
      'GET /api/health/provider/:provider',
      'GET /api/metrics',
      'GET /api/services/status/:provider?',
      'POST /api/services/test/:provider',
      'GET /api/config/summary/:provider?',
      'GET /api/config/voices/:provider?',
      'GET /api/config/models/:provider?'
    ]
  })
}

module.exports = {
  createApiMiddleware,
  requestLoggingMiddleware,
  requestValidationMiddleware,
  performanceMiddleware,
  jsonParsingMiddleware,
  apiSecurityMiddleware,
  validateProviderMiddleware,
  validateQueryParams,
  validateBody,
  apiErrorHandler,
  notFoundHandler
}
