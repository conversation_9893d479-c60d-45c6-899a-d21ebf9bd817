.
├── api
│   ├── controllers
│   │   └── HealthController.js
│   ├── middleware
│   │   └── index.js
│   └── routes
│       └── index.js
├── audio-samples
│   ├── Dalia_sample.mp3
│   ├── Elias_sample.mp3
│   ├── Elvira_sample.mp3
│   ├── Irene_sample.mp3
│   ├── Jorge_sample.mp3
│   ├── Saul_sample.mp3
│   ├── Triana_sample.mp3
│   └── Ximena_DragonHD_sample.mp3
├── config
│   └── validated-voices.json
├── core
│   ├── config
│   │   ├── environment.js
│   │   ├── index.js
│   │   └── security.js
│   ├── index.js
│   ├── services
│   │   └── tts
│   │       └── CustomTTSService.js
│   ├── types
│   │   └── voice.js
│   └── utils
│       ├── helpers.js
│       ├── index.js
│       ├── logger.js
│       ├── performance.js
│       ├── textProcessor.js
│       └── validation.js
├── docs
│   ├── azure_readme.md
│   └── file-system.md
├── .env
├── .gitignore
├── index.js
├── package.json
├── package-lock.json
├── .prettierrc.cjs
├── README.md
├── scripts
│   ├── debug-tts.js
│   ├── discover-real-voices.js
│   ├── investigate-tts-format.js
│   ├── quick-format-test.js
│   ├── update-tts-service.js
│   ├── validate-all-voices.js
│   └── verify-env.js
├── services
│   ├── factory
│   │   ├── ConfigManager.js
│   │   └── ServiceFactory.js
│   ├── interfaces
│   │   ├── IAIService.js
│   │   ├── ISpeechService.js
│   │   └── ITTSService.js
│   └── providers
│       ├── azure
│       │   ├── AzureAIService.js
│       │   ├── AzureSpeechService.js
│       │   └── AzureTTSService.js
│       ├── custom
│       │   ├── CustomAIService.js
│       │   ├── CustomSpeechService.js
│       │   └── CustomTTSService.js
│       └── google
│           ├── GoogleAIService.js
│           ├── GoogleSpeechService.js
│           └── GoogleTTSService.js
├── socket
│   ├── handlers
│   │   └── VoiceSessionHandler.js
│   └── middleware
│       └── index.js
├── voice-validation-results.json
└── yarn.lock

25 directories, 58 files
