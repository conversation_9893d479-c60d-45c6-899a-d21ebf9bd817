.
├── api
│   ├── controllers
│   │   └── HealthController.js
│   ├── middleware
│   │   └── index.js
│   └── routes
│       └── index.js
├── core
│   ├── config
│   │   ├── environment.js
│   │   └── security.js
│   ├── index.js
│   ├── types
│   │   └── voice.js
│   └── utils
│       ├── helpers.js
│       ├── index.js
│       ├── logger.js
│       ├── performance.js
│       ├── textProcessor.js
│       └── validation.js
├── docs
│   ├── azure_readme.md
│   └── file-system.md
├── .env
├── .gitignore
├── index.js
├── index.js.bk
├── package.json
├── package-lock.json
├── .prettierrc.cjs
├── README.md
├── services
│   ├── factory
│   │   ├── ConfigManager.js
│   │   └── ServiceFactory.js
│   ├── interfaces
│   │   ├── IAIService.js
│   │   ├── ISpeechService.js
│   │   └── ITTSService.js
│   └── providers
│       ├── azure
│       │   ├── AzureAIService.js
│       │   ├── AzureSpeechService.js
│       │   └── AzureTTSService.js
│       └── google
│           ├── GoogleAIService.js
│           ├── GoogleSpeechService.js
│           └── GoogleTTSService.js
├── socket
│   ├── handlers
│   │   └── VoiceSessionHandler.js
│   └── middleware
│       └── index.js
└── yarn.lock

19 directories, 37 files
