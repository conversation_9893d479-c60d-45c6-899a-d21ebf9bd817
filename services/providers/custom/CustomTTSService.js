const axios = require('axios')
const { ITTSService } = require('../../interfaces/ITTSService')
const { customLog } = require('../../../core/utils/logger')

class CustomTTSService extends ITTSService {
  constructor(config) {
    super()
    this.config = config || {}
    this.axiosInstance = null
    this.availableVoices = []
    this.currentVoice = null

    this.init()
  }

  init() {
    try {
      // Validar configuración
      if (!this.config.apiUrl) {
        throw new Error('Custom TTS API URL not configured')
      }

      if (!this.config.apiKey) {
        throw new Error('Custom TTS API key not configured')
      }

      // Crear axios instance para TTS
      this.axiosInstance = axios.create({
        baseURL: this.config.apiUrl,
        headers: {
          Authorization: `Bearer ${this.config.apiKey}`
        },
        timeout: this.config.timeout || 30000,
        maxContentLength: this.config.maxContentLength || 10 * 1024 * 1024 // 10MB
      })

      customLog('Custom TTS Service initialized successfully')

    } catch (error) {
      customLog('Error initializing Custom TTS Service:', error.message)
      throw error
    }
  }

  async generateSpeech(text) {
    // Usar validación de la interfaz
    const validatedText = this.validateTextInput(text)

    if (!this.axiosInstance) {
      throw new Error('Custom TTS service not initialized')
    }

    if (!this.currentVoice) {
      // Intentar obtener voz por defecto si no hay una configurada
      await this.loadDefaultVoice()
    }

    try {
      // Limpiar texto para mejor pronunciación
      const cleanedText = this.cleanTextForSpeech(validatedText)

      customLog('Generating speech with Custom TTS for text length:', cleanedText.length)

      // Formato según tu azureVoicesService.ts
      const payload = {
        input_text: cleanedText,
        voice_params: {
          voice_id: this.currentVoice || this.config.defaultVoice || 'es-ES-ElviraNeural',
          rate: this.config.rate || '0%'
        },
        output_format: this.config.outputFormat || 'mp3'
      }

      const response = await this.axiosInstance.post('/t2s', payload, {
        responseType: 'arraybuffer',
        headers: {
          // No añadir Content-Type, dejarlo a axios
        }
      })

      // Validar respuesta
      if (!response.data) {
        throw new Error('Empty response from Custom TTS service')
      }

      if (response.data.byteLength === 0) {
        throw new Error('Custom TTS service returned empty audio data')
      }

      const audioBuffer = Buffer.from(response.data, 'binary')

      // Usar validación de la interfaz
      const validatedBuffer = this.validateAudioOutput(audioBuffer)

      customLog('Custom TTS: Speech generation completed, audio length:', validatedBuffer.length)
      return validatedBuffer

    } catch (error) {
      customLog('Error generating speech with Custom TTS:', error.message || error)
      throw this.handleTTSError(error)
    }
  }

  async getAvailableVoices() {
    try {
      if (!this.axiosInstance) {
        throw new Error('Custom TTS service not initialized')
      }

      // Formato según tu azureVoicesService.ts
      const payload = {
        language: this.config.language || 'es-ES',
        gender: this.config.gender || 'female'
      }

      const response = await this.axiosInstance.post('/available_voices', payload)

      if (Array.isArray(response.data)) {
        this.availableVoices = response.data.map(voice => ({
          name: voice,
          displayName: voice,
          gender: 'unknown',
          language: this.config.language || 'es-ES',
          provider: 'custom'
        }))
      } else {
        // Fallback a voces por defecto
        this.availableVoices = [
          {
            name: 'Elvira',
            displayName: 'Elvira (Spanish Female)',
            gender: 'female',
            language: 'es-ES',
            provider: 'custom'
          }
        ]
      }

      return this.availableVoices

    } catch (error) {
      customLog('Error getting available voices:', error.message)
      // Retornar voces por defecto en caso de error
      return [
        {
          name: 'es-ES-ElviraNeural',
          displayName: 'Elvira (Spanish Female)',
          gender: 'female',
          language: 'es-ES',
          provider: 'custom'
        }
      ]
    }
  }

  async loadDefaultVoice() {
    try {
      const voices = await this.getAvailableVoices()
      if (voices.length > 0) {
        this.currentVoice = voices[0].name
        customLog('Default voice loaded:', this.currentVoice)
      }
    } catch (error) {
      customLog('Error loading default voice:', error.message)
      this.currentVoice = 'es-ES-ElviraNeural' // Fallback
    }
  }

  async setVoice(voiceName) {
    try {
      const voices = await this.getAvailableVoices()
      const voice = voices.find(v => v.name === voiceName)

      if (voice) {
        this.currentVoice = voiceName
        customLog('Voice changed to:', voiceName)
        return true
      } else {
        customLog('Voice not found:', voiceName)
        return false
      }
    } catch (error) {
      customLog('Error setting voice:', error.message)
      return false
    }
  }

  async testConnection() {
    try {
      // Test más simple - solo verificar que el servicio responde
      if (!this.axiosInstance) {
        throw new Error('Service not initialized')
      }

      // Test con el endpoint de voces que ya sabemos que funciona
      const testPayload = {
        language: 'es-ES',
        gender: 'female'
      }

      const response = await this.axiosInstance.post('/available_voices', testPayload, {
        timeout: 5000 // Timeout más corto para health check
      })

      if (response.data) {
        customLog('Custom TTS connection test successful')
        return true
      }

      return false

    } catch (error) {
      customLog('Custom TTS connection test failed:', error.message)

      // Si el error es de timeout o red, pero el servicio está configurado,
      // considerarlo como warning en lugar de failure
      if (error.code === 'ECONNABORTED' || error.code === 'ETIMEDOUT') {
        customLog('Custom TTS: Timeout but service is configured - treating as healthy')
        return true // Considerarlo healthy si está configurado pero lento
      }

      return false
    }
  }

  getStatus() {
    return {
      provider: 'custom',
      service: 'custom-tts',
      isInitialized: this.axiosInstance !== null,
      apiUrl: this.config.apiUrl,
      currentVoice: this.currentVoice,
      availableVoicesCount: this.availableVoices.length,
      outputFormat: this.config.outputFormat || 'mp3',
      timeout: this.config.timeout || 30000
    }
  }

  // Override interface error handling for Custom TTS specific errors
  handleTTSError(error) {
    const errorMessage = error.message || error.toString()

    // HTTP-specific error handling
    if (error.code === 'ECONNREFUSED') {
      return new Error('Custom TTS service is unavailable. Please try again later.')
    } else if (error.code === 'ETIMEDOUT' || errorMessage.includes('timeout')) {
      return new Error('Custom TTS generation timed out. Please try again with shorter text.')
    } else if (error.response) {
      const status = error.response.status
      if (status === 401) {
        return new Error('Custom TTS service authentication failed. Please check API key.')
      } else if (status === 429) {
        return new Error('Custom TTS service rate limit exceeded. Please try again later.')
      } else if (status >= 500) {
        return new Error('Custom TTS service is experiencing issues. Please try again later.')
      } else {
        return new Error(`Custom TTS service error (${status}): ${error.response.statusText || 'Unknown error'}`)
      }
    }

    // Fall back to base interface error handling
    return super.handleTTSError(error)
  }

  // Custom text cleaning for Spanish
  cleanTextForSpeech(text) {
    // Start with base interface cleaning
    let cleanedText = super.cleanTextForSpeech(text)

    // Add custom optimizations
    cleanedText = cleanedText
      // Handle specific brand names and technical terms
      .replace(/Movistar\+/g, 'Movistar Plus')
      .replace(/AI/g, 'inteligencia artificial')
      .replace(/API/g, 'A P I')
      .replace(/URL/g, 'U R L')
      .replace(/HTTP/g, 'H T T P')
      // Handle numbers and currency for better Spanish pronunciation
      .replace(/\b(\d+)%/g, '$1 por ciento')
      .replace(/\b(\d+)€/g, '$1 euros')
      .replace(/\b(\d+)\$/g, '$1 dólares')
      // Handle common abbreviations
      .replace(/\betc\./g, 'etcétera')
      .replace(/\bDr\./g, 'Doctor')
      .replace(/\bSra?\./g, 'Señora')
      .replace(/\bSr\./g, 'Señor')
      .replace(/\bVs\./g, 'versus')

    return cleanedText
  }

  // Voice configuration
  updateVoiceSettings(settings) {
    const { voiceId, rate, outputFormat, timeout, maxContentLength } = settings

    if (voiceId) this.currentVoice = voiceId
    if (rate) this.config.rate = rate
    if (outputFormat) this.config.outputFormat = outputFormat
    if (timeout) this.config.timeout = timeout
    if (maxContentLength) this.config.maxContentLength = maxContentLength

    // Update axios instance if needed
    if (this.axiosInstance) {
      if (timeout) {
        this.axiosInstance.defaults.timeout = timeout
      }
      if (maxContentLength) {
        this.axiosInstance.defaults.maxContentLength = maxContentLength
      }
    }

    customLog('Custom TTS voice settings updated:', settings)
    return this.getStatus()
  }

  // Method to test TTS generation with a simple text
  async testTTSGeneration() {
    try {
      const testText = 'Hola, esta es una prueba rápida.'
      const audio = await this.generateSpeech(testText)
      return audio && audio.length > 0
    } catch (error) {
      customLog('TTS generation test failed:', error.message)
      return false
    }
  }

  // Health check más robusto
  async healthCheck() {
    try {
      // 1. Verificar inicialización
      if (!this.axiosInstance) {
        return { status: 'error', message: 'Service not initialized' }
      }

      // 2. Test de conectividad básica
      const connectivityTest = await this.testConnection()
      if (!connectivityTest) {
        return { status: 'warning', message: 'Connectivity issues but service configured' }
      }

      // 3. Test opcional de generación (comentado para evitar latencia en health checks)
      // const generationTest = await this.testTTSGeneration()
      // if (!generationTest) {
      //   return { status: 'warning', message: 'Service connected but TTS generation failed' }
      // }

      return {
        status: 'healthy',
        message: 'Service fully operational',
        provider: 'custom'
      }

    } catch (error) {
      return {
        status: 'error',
        message: error.message,
        provider: 'custom'
      }
    }
  }

  // Cleanup method
  cleanup() {
    try {
      if (this.axiosInstance) {
        this.axiosInstance = null
      }
      this.availableVoices = []
      this.currentVoice = null
      customLog('Custom TTS Service cleaned up')
    } catch (error) {
      customLog('Error during Custom TTS cleanup:', error.message)
    }
  }
}

module.exports = { CustomTTSService }
