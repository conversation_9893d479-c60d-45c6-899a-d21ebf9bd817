require('dotenv').config()
const { customLog } = require('../../core/utils/logger')

/**
 * Configuration Manager for unified service configuration
 * Handles configuration for multiple providers with validation and defaults
 */
class ConfigManager {
  constructor() {
    this.configs = new Map()
    this.validators = new Map()
    this.defaults = new Map()

    this.initializeDefaults()
    this.initializeValidators()
  }

  /**
   * Initialize default configurations for all providers
   */
  initializeDefaults() {
    // Azure default configuration
    this.defaults.set('azure', {
      speech: {
        subscriptionKey: null,
        region: 'westeurope',
        language: 'es-ES',
        format: 'Detailed',
        profanity: 'Masked',
        enableDictation: true,
        enableWordLevelTimestamps: true,
        enableDiarization: false
      },
      ai: {
        endpoint: null,
        apiKey: null,
        deploymentName: 'gpt-4',
        apiVersion: '2024-08-01-preview',
        maxTokens: 500,
        temperature: 0.7,
        topP: 0.9,
        frequencyPenalty: 0,
        presencePenalty: 0,
        systemPrompt: 'You are a helpful assistant.'
      },
      tts: {
        subscriptionKey: null,
        region: 'westeurope',
        voice: 'es-ES-ElviraNeural',
        outputFormat: 'Audio16Khz32KBitRateMonoMp3',
        speakingRate: '0%',
        pitch: '0%',
        volume: '0%'
      }
    })

    // Google default configuration
    this.defaults.set('google', {
      speech: {
        config: {
          encoding: 'LINEAR16',
          sampleRateHertz: 16000,
          languageCode: 'es-ES',
          model: 'latest_long',
          useEnhanced: true
        },
        interimResults: true
      },
      ai: {
        project: 'sandbox-innovacion-3',
        location: 'europe-southwest1',
        model: 'gemini-2.0-flash-001',
        maxTokens: 500,
        temperature: 0.7,
        topP: 0.9,
        systemPrompt: 'You are a helpful assistant.'
      },
      tts: {
        baseURL: null,
        apiKey: null,
        voiceId: 'Ximena',
        outputFormat: 'mp3',
        timeout: 30000,
        maxContentLength: 10 * 1024 * 1024
      }
    })

    // Custom default configuration (NUEVO)
    this.defaults.set('custom', {
      speech: {
        encoding: 'LINEAR16',
        sampleRateHertz: 16000,
        languageCode: 'es-ES',
        bufferProcessing: true,
        streamTimeout: 60000
      },
      ai: {
        apiUrl: null,
        apiKey: null,
        presetIaVsPlayer: 'mapp-Claude_enygma_V2',
        presetGenCharBot: 'mapp-gen-char-bot',
        timeout: 30000,
        maxTokens: 500,
        temperature: 0.7,
        systemPrompt: 'You are a helpful assistant.'
      },
      tts: {
        apiUrl: null,
        apiKey: null,
        defaultVoice: 'es-ES-ElviraNeural',
        language: 'es-ES',
        gender: 'female',
        rate: '0%',
        outputFormat: 'mp3',
        timeout: 30000,
        maxContentLength: 10 * 1024 * 1024
      }
    })
  }

  /**
   * Initialize configuration validators
   */
  initializeValidators() {
    // Azure validators
    this.validators.set('azure', {
      speech: (config) => {
        const errors = []
        if (!config.subscriptionKey) errors.push('Azure Speech subscription key is required')
        if (!config.region) errors.push('Azure Speech region is required')
        return { valid: errors.length === 0, errors }
      },
      ai: (config) => {
        const errors = []
        if (!config.endpoint) errors.push('Azure OpenAI endpoint is required')
        if (!config.apiKey) errors.push('Azure OpenAI API key is required')
        if (!config.deploymentName) errors.push('Azure OpenAI deployment name is required')
        if (config.temperature < 0 || config.temperature > 2) {
          errors.push('Temperature must be between 0 and 2')
        }
        return { valid: errors.length === 0, errors }
      },
      tts: (config) => {
        const errors = []
        if (!config.subscriptionKey) errors.push('Azure Speech subscription key is required for TTS')
        if (!config.region) errors.push('Azure Speech region is required for TTS')
        return { valid: errors.length === 0, errors }
      }
    })

    // Google validators
    this.validators.set('google', {
      speech: (config) => {
        const errors = []
        if (!config.config) errors.push('Google Speech config object is required')
        if (config.config && !config.config.languageCode) {
          errors.push('Language code is required for Google Speech')
        }
        return { valid: errors.length === 0, errors }
      },
      ai: (config) => {
        const errors = []
        if (!config.project) errors.push('Google Cloud project is required')
        if (!config.location) errors.push('Google Cloud location is required')
        if (config.temperature < 0 || config.temperature > 1) {
          errors.push('Temperature must be between 0 and 1 for Google AI')
        }
        return { valid: errors.length === 0, errors }
      },
      tts: (config) => {
        const errors = []
        if (!config.baseURL) errors.push('TTS service base URL is required')
        if (!config.apiKey) errors.push('TTS service API key is required')
        return { valid: errors.length === 0, errors }
      }
    })

    // Custom validators (NUEVO)
    this.validators.set('custom', {
      speech: (config) => {
        const errors = []
        // Speech service es principalmente para stream, no requiere validación estricta
        return { valid: errors.length === 0, errors }
      },
      ai: (config) => {
        const errors = []
        if (!config.apiUrl) errors.push('Custom AI API URL is required')
        if (!config.apiKey) errors.push('Custom AI API key is required')
        if (!config.presetIaVsPlayer) errors.push('Custom AI preset for IA vs Player is required')
        if (!config.presetGenCharBot) errors.push('Custom AI preset for character generation is required')
        return { valid: errors.length === 0, errors }
      },
      tts: (config) => {
        const errors = []
        if (!config.apiUrl) errors.push('Custom TTS API URL is required')
        if (!config.apiKey) errors.push('Custom TTS API key is required')
        return { valid: errors.length === 0, errors }
      }
    })
  }

  /**
   * Load configuration from environment variables
   * @param {string} provider - Provider name
   * @returns {Object} Configuration object
   */
  loadFromEnvironment(provider) {
    const envConfig = {}

    switch (provider.toLowerCase()) {
      case 'azure':
        envConfig.speech = {
          subscriptionKey: process.env.AZURE_SPEECH_KEY,
          region: process.env.AZURE_SPEECH_REGION
        }
        envConfig.ai = {
          endpoint: process.env.AZURE_OPENAI_ENDPOINT,
          apiKey: process.env.AZURE_OPENAI_KEY,
          deploymentName: process.env.AZURE_OPENAI_DEPLOYMENT_NAME,
          systemPrompt: process.env.AI_PROMPT
        }
        envConfig.tts = {
          subscriptionKey: process.env.AZURE_SPEECH_KEY,
          region: process.env.AZURE_SPEECH_REGION
        }
        break

      case 'google':
        envConfig.speech = {
          // Google Speech uses service account authentication
        }
        envConfig.ai = {
          project: process.env.GOOGLE_CLOUD_PROJECT,
          location: process.env.GOOGLE_CLOUD_LOCATION,
          systemPrompt: process.env.AI_PROMPT
        }
        envConfig.tts = {
          baseURL: process.env.AUDIO_BACKEND_BASE_URL,
          apiKey: process.env.AUDIO_BACKEND_API_KEY
        }
        break

      case 'custom':
        envConfig.speech = {
          // Stream-based speech processing
        }
        envConfig.ai = {
          apiUrl: process.env.IA_API_URL,
          apiKey: process.env.IA_API_KEY,
          presetIaVsPlayer: process.env.IA_PRESETID_IA_VS_PLAYER,
          presetGenCharBot: process.env.IA_PRESETID_GENCHARBOT,
          systemPrompt: process.env.AI_PROMPT
        }
        envConfig.tts = {
          apiUrl: process.env.SPEECH_API_URL,
          apiKey: process.env.SPEECH_API_KEY
        }
        break

      default:
        throw new Error(`Unsupported provider: ${provider}`)
    }

    return this.mergeWithDefaults(provider, envConfig)
  }

  /**
   * Merge configuration with defaults
   * @param {string} provider - Provider name
   * @param {Object} config - Configuration to merge
   * @returns {Object} Merged configuration
   */
  mergeWithDefaults(provider, config) {
    const defaults = this.defaults.get(provider.toLowerCase())
    if (!defaults) {
      throw new Error(`No default configuration found for provider: ${provider}`)
    }

    return this.deepMerge(defaults, config)
  }

  /**
   * Get configuration for a provider
   * @param {string} provider - Provider name
   * @param {Object} overrides - Configuration overrides
   * @returns {Object} Final configuration
   */
  getConfig(provider, overrides = {}) {
    const cacheKey = `${provider}_${JSON.stringify(overrides)}`

    if (this.configs.has(cacheKey)) {
      return this.configs.get(cacheKey)
    }

    // Load from environment and merge with overrides
    const envConfig = this.loadFromEnvironment(provider)
    const finalConfig = this.deepMerge(envConfig, overrides)

    // Validate configuration
    const validation = this.validateConfig(provider, finalConfig)
    if (!validation.valid) {
      throw new Error(`Configuration validation failed for ${provider}: ${validation.errors.join(', ')}`)
    }

    // Cache and return
    this.configs.set(cacheKey, finalConfig)
    customLog(`Configuration loaded for provider: ${provider}`)

    return finalConfig
  }

  /**
   * Validate configuration for a provider
   * @param {string} provider - Provider name
   * @param {Object} config - Configuration to validate
   * @returns {Object} Validation result
   */
  validateConfig(provider, config) {
    const validators = this.validators.get(provider.toLowerCase())
    if (!validators) {
      return { valid: true, errors: [] }
    }

    const allErrors = []

    // Validate each service configuration
    for (const [service, serviceConfig] of Object.entries(config)) {
      if (validators[service]) {
        const result = validators[service](serviceConfig)
        if (!result.valid) {
          allErrors.push(...result.errors.map(error => `${service}: ${error}`))
        }
      }
    }

    return {
      valid: allErrors.length === 0,
      errors: allErrors
    }
  }

  /**
   * Set custom configuration for a provider
   * @param {string} provider - Provider name
   * @param {Object} config - Configuration object
   * @param {boolean} validate - Whether to validate the configuration
   */
  setConfig(provider, config, validate = true) {
    if (validate) {
      const validation = this.validateConfig(provider, config)
      if (!validation.valid) {
        throw new Error(`Invalid configuration for ${provider}: ${validation.errors.join(', ')}`)
      }
    }

    const cacheKey = `${provider}_custom`
    this.configs.set(cacheKey, config)
    customLog(`Custom configuration set for provider: ${provider}`)
  }

  /**
   * Get available voice options for TTS
   * @param {string} provider - Provider name
   * @returns {Array} Available voices
   */
  getAvailableVoices(provider) {
    switch (provider.toLowerCase()) {
      case 'azure':
        return [
          { name: 'es-ES-ElviraNeural', gender: 'female', language: 'Spanish (Spain)' },
          { name: 'es-ES-AlvaroNeural', gender: 'male', language: 'Spanish (Spain)' },
          { name: 'es-ES-AbrilNeural', gender: 'female', language: 'Spanish (Spain)' },
          { name: 'es-ES-ArnauNeural', gender: 'male', language: 'Spanish (Spain)' },
          { name: 'es-MX-DaliaNeural', gender: 'female', language: 'Spanish (Mexico)' },
          { name: 'es-MX-JorgeNeural', gender: 'male', language: 'Spanish (Mexico)' }
        ]

      case 'google':
        return [
          { name: 'Ximena', gender: 'female', language: 'Spanish' },
          { name: 'Carlos', gender: 'male', language: 'Spanish' },
          { name: 'Lucia', gender: 'female', language: 'Spanish (Mexico)' },
          { name: 'Diego', gender: 'male', language: 'Spanish (Argentina)' }
        ]

      case 'custom':
        return [
          { name: 'es-ES-ElviraNeural', gender: 'female', language: 'Spanish (Spain)' },
          { name: 'es-ES-AlvaroNeural', gender: 'male', language: 'Spanish (Spain)' },
          { name: 'es-MX-DaliaNeural', gender: 'female', language: 'Spanish (Mexico)' },
          { name: 'es-MX-JorgeNeural', gender: 'male', language: 'Spanish (Mexico)' }
        ]

      default:
        return []
    }
  }

  /**
   * Get available AI models for a provider
   * @param {string} provider - Provider name
   * @returns {Array} Available models
   */
  getAvailableModels(provider) {
    switch (provider.toLowerCase()) {
      case 'azure':
        return [
          { name: 'gpt-4', description: 'GPT-4 model' },
          { name: 'gpt-4-turbo', description: 'GPT-4 Turbo model' },
          { name: 'gpt-35-turbo', description: 'GPT-3.5 Turbo model' }
        ]

      case 'google':
        return [
          { name: 'gemini-2.0-flash-001', description: 'Gemini 2.0 Flash' },
          { name: 'gemini-1.5-pro', description: 'Gemini 1.5 Pro' },
          { name: 'gemini-1.5-flash', description: 'Gemini 1.5 Flash' }
        ]

      case 'custom':
        return [
          { name: 'mapp-Claude_enygma_V2', description: 'Claude Enygma V2 Preset' },
          { name: 'mapp-gen-char-bot', description: 'Character Generation Preset' }
        ]

      default:
        return []
    }
  }

  /**
   * Deep merge utility
   * @param {Object} target - Target object
   * @param {Object} source - Source object
   * @returns {Object} Merged object
   */
  deepMerge(target, source) {
    const result = { ...target }

    for (const key in source) {
      if (source.hasOwnProperty(key)) {
        if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
          result[key] = this.deepMerge(result[key] || {}, source[key])
        } else if (source[key] !== undefined && source[key] !== null) {
          result[key] = source[key]
        }
      }
    }

    return result
  }

  /**
   * Clear configuration cache
   */
  clearCache() {
    this.configs.clear()
    customLog('Configuration cache cleared')
  }

  /**
   * Get configuration summary for debugging
   * @param {string} provider - Provider name
   * @returns {Object} Configuration summary
   */
  getConfigSummary(provider) {
    try {
      const config = this.getConfig(provider)

      return {
        provider,
        speech: {
          configured: !!(config.speech?.subscriptionKey || config.speech?.config || config.speech?.encoding),
          language: config.speech?.language || config.speech?.config?.languageCode || config.speech?.languageCode,
          model: config.speech?.config?.model || 'stream-based'
        },
        ai: {
          configured: !!(config.ai?.apiKey || config.ai?.project || config.ai?.apiUrl),
          model: config.ai?.deploymentName || config.ai?.model || config.ai?.presetIaVsPlayer,
          endpoint: config.ai?.endpoint || config.ai?.apiUrl ? '[CONFIGURED]' : '[NOT CONFIGURED]'
        },
        tts: {
          configured: !!(config.tts?.subscriptionKey || config.tts?.baseURL || config.tts?.apiUrl),
          voice: config.tts?.voice || config.tts?.voiceId || config.tts?.defaultVoice,
          format: config.tts?.outputFormat
        }
      }
    } catch (error) {
      return {
        provider,
        error: error.message
      }
    }
  }

  /**
   * Export configuration to file (for backup)
   * @param {string} provider - Provider name
   * @returns {Object} Exportable configuration
   */
  exportConfig(provider) {
    const config = this.getConfig(provider)

    // Remove sensitive information
    const safeConfig = JSON.parse(JSON.stringify(config))

    if (safeConfig.speech?.subscriptionKey) {
      safeConfig.speech.subscriptionKey = '[REDACTED]'
    }
    if (safeConfig.ai?.apiKey) {
      safeConfig.ai.apiKey = '[REDACTED]'
    }
    if (safeConfig.tts?.subscriptionKey) {
      safeConfig.tts.subscriptionKey = '[REDACTED]'
    }
    if (safeConfig.tts?.apiKey) {
      safeConfig.tts.apiKey = '[REDACTED]'
    }

    return {
      provider,
      timestamp: new Date().toISOString(),
      config: safeConfig
    }
  }
}

// Singleton instance
const configManager = new ConfigManager()

module.exports = { ConfigManager, configManager }
