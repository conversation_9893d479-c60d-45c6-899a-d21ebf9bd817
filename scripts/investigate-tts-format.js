// scripts/investigate-tts-format.js
require('dotenv').config()
const axios = require('axios')

async function investigateTTSFormat() {
  console.log('🔬 Investigando formato correcto para TTS...\n')

  const config = {
    apiUrl: process.env.SPEECH_API_URL,
    apiKey: process.env.SPEECH_API_KEY
  }

  // Test con diferentes formatos de payload
  const payloadVariations = [
    {
      name: 'Formato Original',
      payload: {
        input_text: 'Hola, esta es una prueba.',
        voice_params: {
          voice_id: 'es-ES-ElviraNeural',
          rate: '0%'
        },
        output_format: 'mp3'
      }
    },
    {
      name: 'Formato Simplificado',
      payload: {
        text: 'Hola, esta es una prueba.',
        voice: 'es-ES-ElviraNeural',
        format: 'mp3'
      }
    },
    {
      name: 'Formato de tu azureVoicesService',
      payload: {
        input_text: '<PERSON><PERSON>, esta es una prueba.',
        voice_params: {
          voice_id: '<PERSON>men<PERSON>'
        },
        output_format: 'mp3'
      }
    },
    {
      name: 'Formato mínimo',
      payload: {
        input_text: 'Hola.',
        voice_params: {
          voice_id: 'es-ES-ElviraNeural'
        }
      }
    },
    {
      name: 'Formato alternativo',
      payload: {
        text: 'Hola.',
        voice_id: 'es-ES-ElviraNeural',
        output_format: 'mp3'
      }
    }
  ]

  for (const variation of payloadVariations) {
    console.log(`📡 Probando: ${variation.name}`)
    console.log(`Payload: ${JSON.stringify(variation.payload, null, 2)}`)

    try {
      const response = await axios.post(`${config.apiUrl}/t2s`, variation.payload, {
        headers: {
          'Authorization': `Bearer ${config.apiKey}`,
          'Content-Type': 'application/json'
        },
        responseType: 'arraybuffer',
        timeout: 10000
      })

      console.log('✅ SUCCESS!')
      console.log(`   Audio size: ${response.data.byteLength} bytes`)
      console.log(`   Content-Type: ${response.headers['content-type'] || 'Not specified'}`)
      console.log(`   🎉 ESTE FORMATO FUNCIONA!\n`)

      // Si encontramos uno que funciona, podemos parar aquí
      break

    } catch (error) {
      console.log('❌ FAILED')
      console.log(`   Error: ${error.message}`)
      if (error.response) {
        console.log(`   Status: ${error.response.status}`)
        if (error.response.data && error.response.data.byteLength < 1000) {
          // Si la respuesta es pequeña, podría ser texto de error
          try {
            const errorText = Buffer.from(error.response.data).toString()
            console.log(`   Error details: ${errorText.substring(0, 200)}...`)
          } catch (e) {
            console.log(`   Error details: [Binary data]`)
          }
        }
      }
      console.log()
    }
  }

  // Investigar available_voices response format
  console.log('🔍 Investigando formato de available_voices...')
  try {
    const response = await axios.post(`${config.apiUrl}/available_voices`, {
      language: 'es-ES',
      gender: 'female'
    }, {
      headers: {
        'Authorization': `Bearer ${config.apiKey}`
      }
    })

    console.log('✅ Available voices response type:', typeof response.data)
    console.log('✅ Available voices data:', JSON.stringify(response.data, null, 2))

    if (Array.isArray(response.data)) {
      console.log(`✅ Es un array con ${response.data.length} elementos`)
      console.log(`✅ Primer elemento: ${response.data[0]}`)
    } else if (typeof response.data === 'object') {
      console.log(`✅ Es un objeto con claves: ${Object.keys(response.data).join(', ')}`)
    }

  } catch (error) {
    console.log('❌ Error en available_voices:', error.message)
  }

  console.log('\n📋 Conclusiones:')
  console.log('- Usa el formato que muestre "SUCCESS" para generateSpeech()')
  console.log('- Revisa el formato de available_voices para getAvailableVoices()')
}

investigateTTSFormat().catch(console.error)
