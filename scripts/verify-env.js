// scripts/verify-env.js
require('dotenv').config()

console.log('🔍 Verificando variables de entorno...\n')

const requiredVars = {
  'API_KEY': process.env.API_KEY,
  'IA_API_URL': process.env.IA_API_URL,
  'IA_API_KEY': process.env.IA_API_KEY,
  'IA_PRESETID_IA_VS_PLAYER': process.env.IA_PRESETID_IA_VS_PLAYER,
  'IA_PRESETID_GENCHARBOT': process.env.IA_PRESETID_GENCHARBOT,
  'SPEECH_API_URL': process.env.SPEECH_API_URL,
  'SPEECH_API_KEY': process.env.SPEECH_API_KEY,
  'DEFAULT_PROVIDER': process.env.DEFAULT_PROVIDER
}

let allGood = true

for (const [name, value] of Object.entries(requiredVars)) {
  if (value) {
    console.log(`✅ ${name}: ${value.substring(0, 20)}${value.length > 20 ? '...' : ''}`)
  } else {
    console.log(`❌ ${name}: NOT SET`)
    allGood = false
  }
}

console.log('\n🔍 Detectando proveedores disponibles...')

// Detectar proveedores
const hasCustom = process.env.IA_API_URL && process.env.SPEECH_API_URL
const hasGoogle = process.env.GOOGLE_CLOUD_PROJECT && process.env.AUDIO_BACKEND_BASE_URL
const hasAzure = process.env.AZURE_SPEECH_KEY && process.env.AZURE_OPENAI_ENDPOINT

console.log(`${hasCustom ? '✅' : '❌'} Custom Provider`)
console.log(`${hasGoogle ? '✅' : '❌'} Google Provider`)
console.log(`${hasAzure ? '✅' : '❌'} Azure Provider`)

console.log('\n📋 Resumen:')
if (allGood && hasCustom) {
  console.log('🎉 Todas las variables requeridas están configuradas')
  console.log('🚀 El proveedor Custom debería funcionar correctamente')
} else {
  console.log('⚠️  Faltan algunas variables de entorno')
  console.log('📝 Revisa tu archivo .env')
}

// Test de conectividad básica a las APIs
console.log('\n🌐 Verificando conectividad...')

async function testConnectivity() {
  const axios = require('axios')

  if (process.env.IA_API_URL && process.env.IA_API_KEY) {
    try {
      console.log('📡 Testing LLM API...')
      const response = await axios.post(`${process.env.IA_API_URL}/generate`, {
        query: 'test',
        preset_id: process.env.IA_PRESETID_IA_VS_PLAYER
      }, {
        headers: {
          'Authorization': `Bearer ${process.env.IA_API_KEY}`,
          'Content-Type': 'application/json'
        },
        timeout: 5000
      })
      console.log('✅ LLM API: Conectada correctamente')
    } catch (error) {
      console.log(`❌ LLM API: Error - ${error.message}`)
    }
  }

  if (process.env.SPEECH_API_URL && process.env.SPEECH_API_KEY) {
    try {
      console.log('📡 Testing TTS API...')
      const response = await axios.post(`${process.env.SPEECH_API_URL}/available_voices`, {
        language: 'es-ES',
        gender: 'female'
      }, {
        headers: {
          'Authorization': `Bearer ${process.env.SPEECH_API_KEY}`
        },
        timeout: 5000
      })
      console.log('✅ TTS API: Conectada correctamente')
    } catch (error) {
      console.log(`❌ TTS API: Error - ${error.message}`)
    }
  }
}

testConnectivity().catch(console.error)
