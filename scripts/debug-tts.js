// scripts/debug-tts.js
require('dotenv').config()
const axios = require('axios')

async function debugTTS() {
  console.log('🔍 Debugging Custom TTS Service...\n')

  const config = {
    apiUrl: process.env.SPEECH_API_URL,
    apiKey: process.env.SPEECH_API_KEY
  }

  console.log('📋 Configuración:')
  console.log(`API URL: ${config.apiUrl}`)
  console.log(`API Key: ${config.apiKey ? config.apiKey.substring(0, 10) + '...' : 'NOT SET'}\n`)

  if (!config.apiUrl || !config.apiKey) {
    console.log('❌ Faltan configuraciones básicas')
    return
  }

  // Test 1: Available Voices (Ya sabemos que funciona)
  console.log('📡 Test 1: Available Voices...')
  try {
    const response = await axios.post(`${config.apiUrl}/available_voices`, {
      language: 'es-ES',
      gender: 'female'
    }, {
      headers: {
        'Authorization': `Bearer ${config.apiKey}`
      },
      timeout: 5000
    })

    console.log('✅ Available Voices: OK')
    console.log(`   Voices found: ${Array.isArray(response.data) ? response.data.length : 'Unknown format'}`)
    if (Array.isArray(response.data)) {
      console.log(`   First voice: ${response.data[0] || 'None'}`)
    }
  } catch (error) {
    console.log('❌ Available Voices: FAILED')
    console.log(`   Error: ${error.message}`)
  }

  console.log()

  // Test 2: Text-to-Speech generation
  console.log('📡 Test 2: TTS Generation...')
  try {
    const response = await axios.post(`${config.apiUrl}/t2s`, {
      input_text: 'Hola, esta es una prueba.',
      voice_params: {
        voice_id: 'es-ES-ElviraNeural',
        rate: '0%'
      },
      output_format: 'mp3'
    }, {
      headers: {
        'Authorization': `Bearer ${config.apiKey}`
      },
      responseType: 'arraybuffer',
      timeout: 10000
    })

    console.log('✅ TTS Generation: OK')
    console.log(`   Audio size: ${response.data.byteLength} bytes`)
    console.log(`   Content-Type: ${response.headers['content-type'] || 'Not specified'}`)
  } catch (error) {
    console.log('❌ TTS Generation: FAILED')
    console.log(`   Error: ${error.message}`)
    if (error.response) {
      console.log(`   Status: ${error.response.status}`)
      console.log(`   Status Text: ${error.response.statusText}`)
    }
  }

  console.log()

  // Test 3: Health endpoint (si existe)
  console.log('📡 Test 3: Health Check...')
  try {
    const response = await axios.get(`${config.apiUrl}/health`, {
      headers: {
        'Authorization': `Bearer ${config.apiKey}`
      },
      timeout: 5000
    })

    console.log('✅ Health Check: OK')
    console.log(`   Response: ${JSON.stringify(response.data)}`)
  } catch (error) {
    console.log('⚠️  Health Check: No disponible (esto es normal)')
    console.log(`   Error: ${error.message}`)
  }

  console.log()

  // Test 4: Axios instance similar al servicio
  console.log('📡 Test 4: Simulando CustomTTSService...')
  try {
    const axiosInstance = axios.create({
      baseURL: config.apiUrl,
      headers: {
        Authorization: `Bearer ${config.apiKey}`
      },
      timeout: 30000
    })

    // Test igual que en testConnection()
    const testPayload = {
      language: 'es-ES',
      gender: 'female'
    }

    const response = await axiosInstance.post('/available_voices', testPayload, {
      timeout: 5000
    })

    if (response.data) {
      console.log('✅ CustomTTSService simulation: OK')
      console.log('   El servicio debería pasar el health check')
    } else {
      console.log('❌ CustomTTSService simulation: No data')
    }

  } catch (error) {
    console.log('❌ CustomTTSService simulation: FAILED')
    console.log(`   Error: ${error.message}`)
    console.log(`   Code: ${error.code}`)

    if (error.code === 'ECONNABORTED' || error.code === 'ETIMEDOUT') {
      console.log('   💡 Esto es un timeout - el servicio puede estar lento pero funcional')
    }
  }

  console.log('\n📋 Resumen:')
  console.log('Si los tests 1 y 2 pasan, el TTS service está funcionando correctamente.')
  console.log('Si solo falla el test 4, es probable que sea un problema de timeout en health check.')
}

debugTTS().catch(console.error)
