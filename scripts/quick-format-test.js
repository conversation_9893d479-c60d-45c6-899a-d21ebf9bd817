// scripts/quick-format-test.js
require('dotenv').config()
const axios = require('axios')

async function quickFormatTest() {
  console.log('🔬 Prueba rápida de formato TTS...\n')

  const config = {
    apiUrl: process.env.SPEECH_API_URL,
    apiKey: process.env.SPEECH_API_KEY
  }

  if (!config.apiUrl || !config.apiKey) {
    console.log('❌ Faltan configuraciones de entorno')
    return
  }

  // Usar Elvira que sabemos que funcionó en tu primer script
  const testVoice = 'Elvira'
  const testText = 'Ho<PERSON>, esta es una prueba rápida.'

  const payloadVariations = [
    {
      name: 'Formato Original (voice_id)',
      payload: {
        input_text: testText,
        voice_id: testVoice
      }
    },
    {
      name: 'Formato voice_name',
      payload: {
        input_text: testText,
        voice_name: testVoice
      }
    },
    {
      name: 'Formato con voice_params',
      payload: {
        input_text: testText,
        voice_params: {
          voice_name: testVoice
        }
      }
    },
    {
      name: 'Formato con voice_params y voice_id',
      payload: {
        input_text: testText,
        voice_params: {
          voice_id: testVoice
        }
      }
    },
    {
      name: 'Formato simple voice',
      payload: {
        input_text: testText,
        voice: testVoice
      }
    },
    {
      name: 'Formato con output_format',
      payload: {
        input_text: testText,
        voice_name: testVoice,
        output_format: 'mp3'
      }
    }
  ]

  let workingFormat = null

  for (const variation of payloadVariations) {
    console.log(`🔍 Probando: ${variation.name}`)
    console.log(`   Payload: ${JSON.stringify(variation.payload, null, 2)}`)

    try {
      const response = await axios.post(`${config.apiUrl}/t2s`, variation.payload, {
        headers: {
          'Authorization': `Bearer ${config.apiKey}`,
          'Content-Type': 'application/json'
        },
        responseType: 'arraybuffer',
        timeout: 10000
      })

      console.log(`   ✅ ¡ÉXITO!`)
      console.log(`   📊 Audio size: ${response.data.byteLength} bytes`)
      console.log(`   📋 Content-Type: ${response.headers['content-type'] || 'Not specified'}`)

      workingFormat = variation
      console.log(`   🎉 ¡FORMATO ENCONTRADO!\n`)
      break

    } catch (error) {
      console.log(`   ❌ Falló: ${error.message}`)

      if (error.response) {
        console.log(`   📊 Status: ${error.response.status}`)

        // Mostrar detalles del error si es texto
        if (error.response.data && error.response.data.byteLength < 1000) {
          try {
            const errorText = Buffer.from(error.response.data).toString()
            const errorObj = JSON.parse(errorText)
            console.log(`   🔍 Error details: ${JSON.stringify(errorObj, null, 2)}`)
          } catch (e) {
            try {
              const errorText = Buffer.from(error.response.data).toString()
              console.log(`   🔍 Error text: ${errorText.substring(0, 200)}`)
            } catch (e2) {
              console.log(`   🔍 Error details: [Binary data]`)
            }
          }
        }
      }
      console.log()
    }
  }

  if (workingFormat) {
    console.log('🎯 RESULTADO FINAL:')
    console.log('===================')
    console.log(`✅ Formato que funciona: ${workingFormat.name}`)
    console.log('📋 Payload correcto:')
    console.log(JSON.stringify(workingFormat.payload, null, 2))
    console.log()
    console.log('🔧 Para usar en tu servicio:')
    console.log('```javascript')
    console.log('const payload = {')
    Object.entries(workingFormat.payload).forEach(([key, value]) => {
      if (key === 'input_text') {
        console.log(`  ${key}: text, // Tu texto a convertir`)
      } else if (typeof value === 'object') {
        console.log(`  ${key}: ${JSON.stringify(value)},`)
      } else {
        console.log(`  ${key}: "${value}",`)
      }
    })
    console.log('}')
    console.log('```')
  } else {
    console.log('❌ Ningún formato funcionó. Revisa la configuración del API.')
  }
}

quickFormatTest().catch(console.error)
