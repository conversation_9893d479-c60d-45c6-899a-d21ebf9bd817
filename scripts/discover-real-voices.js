// scripts/discover-real-voices.js
require('dotenv').config()
const axios = require('axios')

async function discoverRealVoices() {
  console.log('🔍 Descubriendo voces reales disponibles...\n')

  const config = {
    apiUrl: process.env.SPEECH_API_URL,
    apiKey: process.env.SPEECH_API_KEY
  }

  // Test 1: Diferentes formas de llamar available_voices
  const voiceQueries = [
    { language: 'es-ES', gender: 'female' },
    { language: 'es-ES', gender: 'male' },
    { language: 'es-ES' },
    { language: 'es' },
    { gender: 'female' },
    { gender: 'male' },
    {} // Sin parámetros
  ]

  for (const query of voiceQueries) {
    console.log(`📡 Probando available_voices con: ${JSON.stringify(query)}`)
    try {
      const response = await axios.post(`${config.apiUrl}/available_voices`, query, {
        headers: {
          'Authorization': `Bearer ${config.apiKey}`
        }
      })

      console.log(`✅ Response type: ${typeof response.data}`)
      console.log(`✅ Response content: "${response.data}"`)
      console.log(`✅ Response length: ${response.data.length}`)

      if (response.data && response.data.length > 0) {
        console.log('🎉 Encontramos respuesta con contenido!')

        // Si es string, intentar parsearlo
        if (typeof response.data === 'string') {
          try {
            const parsed = JSON.parse(response.data)
            console.log('✅ Parsed JSON:', JSON.stringify(parsed, null, 2))
          } catch (e) {
            console.log('⚠️  No es JSON válido, es texto plano')
          }
        }
      }

    } catch (error) {
      console.log(`❌ Error: ${error.message}`)
    }
    console.log()
  }

  // Test 2: Intentar otros endpoints posibles
  const possibleEndpoints = [
    '/voices',
    '/list_voices',
    '/get_voices',
    '/voice_list',
    '/models',
    '/status',
    '/info'
  ]

  console.log('🔍 Probando otros endpoints posibles...')
  for (const endpoint of possibleEndpoints) {
    try {
      const response = await axios.get(`${config.apiUrl}${endpoint}`, {
        headers: {
          'Authorization': `Bearer ${config.apiKey}`
        },
        timeout: 3000
      })

      console.log(`✅ ${endpoint}: Status ${response.status}`)
      if (response.data && typeof response.data === 'object') {
        console.log(`   Data: ${JSON.stringify(response.data).substring(0, 200)}...`)
      } else if (response.data) {
        console.log(`   Data: ${response.data.toString().substring(0, 200)}...`)
      }

    } catch (error) {
      if (error.response && error.response.status !== 404) {
        console.log(`⚠️  ${endpoint}: ${error.response.status} - ${error.message}`)
      }
    }
  }

  // Test 3: Probar TTS con voces comunes españolas
  console.log('\n🎤 Probando TTS con voces comunes...')
  const commonSpanishVoices = [
    'Elvira',
    'Alvaro',
    'es-ES-Elvira',
    'es-ES-Alvaro',
    'Spanish-Female',
    'Spanish-Male',
    'Conchita',
    'Enrique',
    'es_ES_female',
    'es_ES_male',
    'female',
    'male',
    'default'
  ]

  for (const voice of commonSpanishVoices) {
    console.log(`🎵 Probando voz: ${voice}`)
    try {
      const payload = {
        input_text: 'Hola.',
        voice_params: {
          voice_id: voice,
          rate: 0.0  // Número en lugar de string
        },
        output_format: 'mp3'
      }

      const response = await axios.post(`${config.apiUrl}/t2s`, payload, {
        headers: {
          'Authorization': `Bearer ${config.apiKey}`
        },
        responseType: 'arraybuffer',
        timeout: 10000
      })

      console.log(`✅ SUCCESS con voz: ${voice}`)
      console.log(`   Audio size: ${response.data.byteLength} bytes`)
      console.log(`🎉 ESTA VOZ FUNCIONA: ${voice}\n`)

      // Si encontramos una que funciona, probar algunas más para ver el patrón

    } catch (error) {
      if (error.response && error.response.status === 400) {
        // Voz no permitida - normal
      } else {
        console.log(`❌ Error con ${voice}: ${error.message}`)
      }
    }
  }

  // Test 4: Formato correcto con rate como número
  console.log('🔧 Probando formato corregido...')
  const correctedPayload = {
    input_text: 'Hola, prueba de formato corregido.',
    voice_params: {
      voice_id: 'default', // Usar default como fallback
      rate: 0.0
    },
    output_format: 'mp3'
  }

  try {
    const response = await axios.post(`${config.apiUrl}/t2s`, correctedPayload, {
      headers: {
        'Authorization': `Bearer ${config.apiKey}`
      },
      responseType: 'arraybuffer',
      timeout: 10000
    })

    console.log('✅ Formato corregido funciona!')
    console.log(`   Audio size: ${response.data.byteLength} bytes`)

  } catch (error) {
    console.log(`❌ Formato corregido falló: ${error.message}`)
    if (error.response) {
      console.log(`   Status: ${error.response.status}`)
    }
  }

  console.log('\n📋 Resumen de hallazgos:')
  console.log('- Revisa los resultados de arriba para ver qué voces funcionan')
  console.log('- El rate debe ser número (0.0) no string ("0%")')
  console.log('- El campo input_text es obligatorio')
  console.log('- Usa las voces que muestren "SUCCESS"')
}

discoverRealVoices().catch(console.error)
